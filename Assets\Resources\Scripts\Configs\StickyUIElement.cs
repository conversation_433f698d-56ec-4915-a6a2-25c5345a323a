using UnityEngine;
using UnityEngine.UI;

public class StickyUIElement : MonoBehaviour // Makes a copy of stock section stick to the top of the scroll
{
    public PartyConfigs partyConfigs;

    RectTransform stickyOriginal;  // The original sticky element inside your grid (single party GameObject)
    public GameObject stickyCopy;          // The sticky copy that appears fixed at top
    public ScrollRect scrollRect;           // Your ScrollRect component
    public RectTransform scrollContent;    // The scroll content RectTransform
    public float stickThreshold = 5f;      // Threshold for sticking (tweak as needed)

    public bool isStuck = false;

    void Start()
    {
        stickyCopy.SetActive(false);
        SwitchStickyOriginal(partyConfigs.partyIndex);
    }

    void Update()
    {
        if (stickyOriginal == null || stickyCopy == null || scrollRect == null || scrollContent == null)
            return;

        // Calculate scroll info
        float viewportHeight = scrollRect.viewport.rect.height;
        float contentHeight = scrollContent.rect.height;
        float scrollY = (1 - scrollRect.verticalNormalizedPosition) * (contentHeight - viewportHeight);

        // Get stickyOriginal anchoredPosition.y relative to scrollContent (or its parent grid)
        float stickyPosY = stickyOriginal.anchoredPosition.y;

        // Distance from stickyOriginal to viewport top (viewport top is at scrollY = 0)
        float distanceToTop = stickyPosY + scrollY;

        // If stickyOriginal scrolls above the top (distanceToTop > threshold), show stickyCopy
        if (!isStuck && distanceToTop > stickThreshold)
        {
            stickyCopy.SetActive(true);
            stickyOriginal.GetComponent<CanvasGroup>().alpha = 0;
            isStuck = true;
        }
        // If stickyOriginal moves back into view (distanceToTop < threshold), hide stickyCopy
        else if (isStuck && distanceToTop < stickThreshold)
        {
            stickyCopy.SetActive(false);
            stickyOriginal.GetComponent<CanvasGroup>().alpha = 1;
            isStuck = false;
        }
    }

    public void SwitchStickyOriginal(int partyIndex)
    {
        // Since we now have a single party GameObject, we always reference the same stock section (child 2)
        // The partyIndex parameter is kept for compatibility but not needed for switching GameObjects
        stickyOriginal = partyConfigs.transform.GetChild(0).GetChild(2).GetComponent<RectTransform>();
    }
}
