using System;
using System.Collections.Generic;
using UnityEngine;

// Wrapper classes for Lists (Unity JsonUtility can't serialize Lists directly)
[Serializable]
public class JsonCharactersList
{
    public List<JsonBattleCharacter> characters;

    public JsonCharactersList()
    {
        characters = new List<JsonBattleCharacter>();
    }

    public JsonCharactersList(List<JsonBattleCharacter> characters)
    {
        this.characters = characters ?? new List<JsonBattleCharacter>();
    }
}

[Serializable]
public class JsonSkillsList
{
    public List<JsonCharacterSkills> skills;

    public JsonSkillsList()
    {
        skills = new List<JsonCharacterSkills>();
    }

    public JsonSkillsList(List<JsonCharacterSkills> skills)
    {
        this.skills = skills ?? new List<JsonCharacterSkills>();
    }
}

[Serializable]
public class JsonStatusList
{
    public List<JsonCharacterStatus> stats;

    public JsonStatusList()
    {
        stats = new List<JsonCharacterStatus>();
    }

    public JsonStatusList(List<JsonCharacterStatus> stats)
    {
        this.stats = stats ?? new List<JsonCharacterStatus>();
    }
}

[Serializable]
public class JsonModsList
{
    public List<JsonCharacterMods> mods;

    public JsonModsList()
    {
        mods = new List<JsonCharacterMods>();
    }

    public JsonModsList(List<JsonCharacterMods> mods)
    {
        this.mods = mods ?? new List<JsonCharacterMods>();
    }
}

[Serializable]
public class JsonPartiesList
{
    public List<JsonPartyCharacters> parties;

    public JsonPartiesList()
    {
        parties = new List<JsonPartyCharacters>();
    }

    public JsonPartiesList(List<JsonPartyCharacters> parties)
    {
        this.parties = parties ?? new List<JsonPartyCharacters>();
    }
}


/// <summary>
/// JSON-serializable data models for the save/load system
/// These classes provide a bridge between the current data structures and JSON format
/// </summary>

/// <summary>
/// JSON model for individual keyword entries in the localization system
/// Contains id, key, display word, and associated tags for categorization
/// </summary>
[Serializable]
public class JsonKeyword
{
    public string id;           // Unique identifier (e.g., "cw100")
    public string key;          // Code reference constant (e.g., "GENERIC_ACCEPT")
    public string word;         // Display text (e.g., "Confirmar")
    public string[] keywordTags; // Category tags (e.g., ["KWT4"])

    public JsonKeyword() { }

    public JsonKeyword(string id, string key, string word, string[] keywordTags)
    {
        this.id = id ?? "";
        this.key = key ?? "";
        this.word = word ?? "";
        this.keywordTags = keywordTags ?? new string[0];
    }
}

/// <summary>
/// JSON model for keyword package containing array of keywords
/// Used for organizing keywords in the generalInfo.json structure
/// </summary>
[Serializable]
public class JsonKeywordPkg
{
    public JsonKeyword[] keywordPkg;

    public JsonKeywordPkg()
    {
        keywordPkg = new JsonKeyword[0];
    }

    public JsonKeywordPkg(JsonKeyword[] keywords)
    {
        keywordPkg = keywords ?? new JsonKeyword[0];
    }
}

/// <summary>
/// JSON model for individual modifier entries in the localization system
/// Contains id, skill name, description, and acronym for game modifiers
/// </summary>
[Serializable]
public class JsonModifier
{
    public string id;          // Unique identifier (e.g., "MO0")
    public string skill;       // Skill name (e.g., "Inteligência")
    public string description; // Description text (e.g., "Influencia a precisão...")
    public string acronym;     // Short acronym (e.g., "Qi")

    public JsonModifier() { }

    public JsonModifier(string id, string skill, string description, string acronym)
    {
        this.id = id ?? "";
        this.skill = skill ?? "";
        this.description = description ?? "";
        this.acronym = acronym ?? "";
    }
}

/// <summary>
/// JSON model for individual tier entries in the localization system
/// Contains id, name, variance, color, acronym, and selectDrop for character rarity tiers
/// </summary>
[Serializable]
public class JsonTier
{
    public string id;         // Unique identifier (e.g., "TR1")
    public string name;       // Display name (e.g., "Épico")
    public string variance;   // Category type (e.g., "Character Rarity")
    public string color;      // Hex color code (e.g., "#7030a0")
    public string acronym;    // Short abbreviation (e.g., "Epi")
    public string selectDrop; // Dropdown category (e.g., "Character Rarity")

    public JsonTier() { }

    public JsonTier(string id, string name, string variance, string color, string acronym, string selectDrop)
    {
        this.id = id ?? "";
        this.name = name ?? "";
        this.variance = variance ?? "";
        this.color = color ?? "";
        this.acronym = acronym ?? "";
        this.selectDrop = selectDrop ?? "";
    }
}

/// <summary>
/// JSON model for knowledge entries within moon phase data
/// Contains id and value for knowledge modifiers
/// </summary>
[Serializable]
public class JsonMoonKnowledge
{
    public string id;    // Knowledge identifier (e.g., "KN1")
    public string value; // Knowledge value (e.g., "0.15")

    public JsonMoonKnowledge() { }

    public JsonMoonKnowledge(string id, string value)
    {
        this.id = id ?? "";
        this.value = value ?? "";
    }
}

/// <summary>
/// JSON model for attribute entries within moon phase data
/// Contains id and value for attribute modifiers
/// </summary>
[Serializable]
public class JsonMoonAttribute
{
    public string id;    // Attribute identifier (e.g., "AT1")
    public string value; // Attribute value (e.g., "0.25")

    public JsonMoonAttribute() { }

    public JsonMoonAttribute(string id, string value)
    {
        this.id = id ?? "";
        this.value = value ?? "";
    }
}

/// <summary>
/// JSON model for moon phase entries in the localization system
/// Contains id, moonPhase, technicalNomenclature, and nested arrays for knowledge/attributes
/// </summary>
[Serializable]
public class JsonMoonRange
{
    public string id;                      // Unique identifier (e.g., "MMRANG24")
    public string moonPhase;               // Display name with emoji (e.g., "🌑 Nova")
    public string technicalNomenclature;   // Technical constant (e.g., "LUA_NOVA")
    public JsonMoonKnowledge[] knowledge;  // Array of knowledge modifiers
    public JsonMoonAttribute[] attribute;  // Array of attribute modifiers
    public string modDanoParty;           // Party damage modifier (e.g., "-0,1")
    public string modDanoOponente;        // Opponent damage modifier (e.g., "-0,1")

    public JsonMoonRange()
    {
        knowledge = new JsonMoonKnowledge[0];
        attribute = new JsonMoonAttribute[0];
    }

    public JsonMoonRange(string id, string moonPhase, string technicalNomenclature,
                        JsonMoonKnowledge[] knowledge, JsonMoonAttribute[] attribute,
                        string modDanoParty, string modDanoOponente)
    {
        this.id = id ?? "";
        this.moonPhase = moonPhase ?? "";
        this.technicalNomenclature = technicalNomenclature ?? "";
        this.knowledge = knowledge ?? new JsonMoonKnowledge[0];
        this.attribute = attribute ?? new JsonMoonAttribute[0];
        this.modDanoParty = modDanoParty ?? "";
        this.modDanoOponente = modDanoOponente ?? "";
    }
}

/// <summary>
/// JSON model for the complete generalInfo.json file structure
/// Contains keyword package, modifier package, tier package, moon phase package, and can be extended for other general information
/// </summary>
[Serializable]
public class JsonGeneralInfo
{
    public JsonKeyword[] keywordPkg;
    public JsonModifier[] modifierListPkg;
    public JsonTier[] tierListPkg;
    public JsonMoonRange[] modMoonRangesPkg;

    public JsonGeneralInfo()
    {
        keywordPkg = new JsonKeyword[0];
        modifierListPkg = new JsonModifier[0];
        tierListPkg = new JsonTier[0];
        modMoonRangesPkg = new JsonMoonRange[0];
    }

    public JsonGeneralInfo(JsonKeyword[] keywords)
    {
        keywordPkg = keywords ?? new JsonKeyword[0];
        modifierListPkg = new JsonModifier[0];
        tierListPkg = new JsonTier[0];
        modMoonRangesPkg = new JsonMoonRange[0];
    }

    public JsonGeneralInfo(JsonKeyword[] keywords, JsonModifier[] modifiers)
    {
        keywordPkg = keywords ?? new JsonKeyword[0];
        modifierListPkg = modifiers ?? new JsonModifier[0];
        tierListPkg = new JsonTier[0];
        modMoonRangesPkg = new JsonMoonRange[0];
    }

    public JsonGeneralInfo(JsonKeyword[] keywords, JsonModifier[] modifiers, JsonTier[] tiers)
    {
        keywordPkg = keywords ?? new JsonKeyword[0];
        modifierListPkg = modifiers ?? new JsonModifier[0];
        tierListPkg = tiers ?? new JsonTier[0];
        modMoonRangesPkg = new JsonMoonRange[0];
    }

    public JsonGeneralInfo(JsonKeyword[] keywords, JsonModifier[] modifiers, JsonTier[] tiers, JsonMoonRange[] moonRanges)
    {
        keywordPkg = keywords ?? new JsonKeyword[0];
        modifierListPkg = modifiers ?? new JsonModifier[0];
        tierListPkg = tiers ?? new JsonTier[0];
        modMoonRangesPkg = moonRanges ?? new JsonMoonRange[0];
    }
}

[Serializable]
public class JsonSkillValues
{
    public string spDef;
    public string spAtk;
    public string type;
    public string acronym;

    public JsonSkillValues() { }

    public JsonSkillValues(SkillValues skillValues)
    {
        spDef = skillValues.spDef;
        spAtk = skillValues.spAtk;
        type = skillValues.type;
        acronym = skillValues.acronym;
    }

    public SkillValues ToSkillValues()
    {
        return new SkillValues(spDef ?? "0", spAtk ?? "0", type ?? "", acronym ?? "");
    }
}

[Serializable]
public class JsonCharacterSkills
{
    public JsonSkillValues[] skillsByType; // Array indexed by Types enum values

    public JsonCharacterSkills()
    {
        skillsByType = new JsonSkillValues[Enum.GetValues(typeof(Types)).Length];
    }

    public JsonCharacterSkills(CharacterSkills skills)
    {
        skillsByType = new JsonSkillValues[Enum.GetValues(typeof(Types)).Length];

        for (int i = 0; i < skillsByType.Length; i++)
        {
            var skillValues = skills.GetValues((Types)i);
            skillsByType[i] = new JsonSkillValues(skillValues);
        }
    }

    public CharacterSkills ToCharacterSkills()
    {
        CharacterSkills skills = new CharacterSkills();
        for (int i = 0; i < skillsByType.Length && i < Enum.GetValues(typeof(Types)).Length; i++)
        {
            if (skillsByType[i] != null)
            {
                // For backward compatibility, if type/acronym are missing, generate them from the enum
                if (string.IsNullOrEmpty(skillsByType[i].type) || string.IsNullOrEmpty(skillsByType[i].acronym))
                {
                    Types enumType = (Types)i;
                    string typeName = enumType.ToString();
                    string acronym = typeName.Length >= 2 ? typeName[..2] : typeName;
                    skillsByType[i].type = typeName;
                    skillsByType[i].acronym = acronym;
                }

                // Ensure spDef and spAtk are strings (for backward compatibility with int values)
                if (skillsByType[i].spDef == null) skillsByType[i].spDef = "0";
                if (skillsByType[i].spAtk == null) skillsByType[i].spAtk = "0";
                skills.SetValues((Types)i, skillsByType[i].ToSkillValues());
            }
        }
        return skills;
    }
}

[Serializable]
public class JsonCharacterStatus
{
    public List<int> apMin;
    public List<int> hp;
    public List<int> atk;
    public List<int> def;
    public List<int> atkLim;
    public List<int> bl;

    public JsonCharacterStatus() { }

    public JsonCharacterStatus(CharacterStatus status)
    {
        apMin = new List<int>(status.GetApMin());
        hp = new List<int>(status.GetHp());
        atk = new List<int>(status.GetAtk());
        def = new List<int>(status.GetDef());
        atkLim = new List<int>(status.GetAtkLim());
        bl = new List<int>(status.GetBl());
    }

    public CharacterStatus ToCharacterStatus()
    {
        CharacterStatus status = new CharacterStatus();
        if (apMin != null) status.UpdateApMin(apMin);
        if (hp != null) status.UpdateHp(hp);
        if (atk != null) status.UpdateAtk(atk);
        if (def != null) status.UpdateDef(def);
        if (atkLim != null) status.UpdateAtkLim(atkLim);
        status.UpdateBl(); // This regenerates bl based on apMin count
        return status;
    }
}

[Serializable]
public class JsonCharacterMods
{
    public int knowledge;
    public int luck;
    public int speed;
    public int precision;
    public int evasion;
    public int criticalChance;
    public int parryChance;

    public JsonCharacterMods() { }

    public JsonCharacterMods(CharacterMods mods)
    {
        knowledge = mods.GetKnowledge();
        luck = mods.GetLuck();
        speed = mods.GetSpeed();
        precision = mods.GetPrecision();
        evasion = mods.GetEvasion();
        criticalChance = mods.GetCriticalChance();
        parryChance = mods.GetParryChance();
    }

    public CharacterMods ToCharacterMods()
    {
        CharacterMods mods = new CharacterMods();
        mods.SetKnowledge(knowledge);
        mods.SetLuck(luck);
        mods.SetSpeed(speed);
        mods.SetPrecision(precision);
        mods.SetEvasion(evasion);
        mods.SetCriticalChance(criticalChance);
        mods.SetParryChance(parryChance);
        return mods;
    }
}

[Serializable]
public class JsonCharacterAilmentDefenses
{
    public string charm;
    public string confusion;
    public string curse;
    public string paralysis;
    public string sleep;

    public JsonCharacterAilmentDefenses() { }

    public JsonCharacterAilmentDefenses(CharacterAilementDefenses ailDefs)
    {
        charm = ailDefs.GetCharm();
        confusion = ailDefs.GetConfusion();
        curse = ailDefs.GetCurse();
        paralysis = ailDefs.GetParalysis();
        sleep = ailDefs.GetSleep();
    }

    public CharacterAilementDefenses ToCharacterAilmentDefenses()
    {
        CharacterAilementDefenses ailDefs = new CharacterAilementDefenses();
        ailDefs.SetCharm(charm);
        ailDefs.SetConfusion(confusion);
        ailDefs.SetCurse(curse);
        ailDefs.SetParalysis(paralysis);
        ailDefs.SetSleep(sleep);
        return ailDefs;
    }
}

[Serializable]
public class JsonBattleCharacter
{
    public string id;
    public string name;
    public string description; // Character description text
    public string title;       // Character title/subtitle
    public string birthyear;   // Birth year (can be "?" for unknown)
    public string height;      // Character height
    public string age;         // Character age
    public string gender;      // Character gender
    public string classe;

    public string rarity;
    public int baseLevel;
    public int level;
    public bool isEnemy;
    public JsonCharacterSkills skills;
    public JsonCharacterStatus stats;
    public JsonCharacterMods mods;
    public JsonCharacterAilmentDefenses ailDefs;

    public JsonBattleCharacter() { }

    public JsonBattleCharacter(BattleCharacter character)
    {
        if (character == null)
        {
            Debug.LogError("[JsonBattleCharacter] ❌ Cannot create JsonBattleCharacter from null character");
            return;
        }

        id = character.id;
        name = character.name;
        classe = character.classe;
        rarity = character.rarity;
        description = character.description ?? string.Empty;
        title = character.title ?? string.Empty;
        birthyear = character.birthyear ?? string.Empty;
        height = character.height ?? string.Empty;
        age = character.age ?? string.Empty;
        gender = character.gender ?? string.Empty;

        baseLevel = character.baseLevel;
        level = character.level;
        isEnemy = character.isEnemy;

        try
        {
            skills = new JsonCharacterSkills(character.skills);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert skills for character {character.id}: {ex.Message}");
            skills = new JsonCharacterSkills();
        }

        try
        {
            stats = new JsonCharacterStatus(character.stats);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert stats for character {character.id}: {ex.Message}");
            stats = new JsonCharacterStatus();
        }

        try
        {
            mods = new JsonCharacterMods(character.mods);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert mods for character {character.id}: {ex.Message}");
            mods = new JsonCharacterMods();
        }

        try
        {
            ailDefs = new JsonCharacterAilmentDefenses(character.ailDefs);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonBattleCharacter] ❌ Failed to convert ailDefs for character {character.id}: {ex.Message}");
            ailDefs = new JsonCharacterAilmentDefenses();
        }
    }

    public BattleCharacter ToBattleCharacter()
    {
        BattleCharacter character = new BattleCharacter(id);
        character.name = name;
        character.classe = classe;
        character.rarity = rarity;
        character.description = description ?? string.Empty;
        character.title = title ?? string.Empty;
        character.birthyear = birthyear ?? string.Empty;
        character.height = height ?? string.Empty;
        character.age = age ?? string.Empty;
        character.gender = gender ?? string.Empty;

        character.baseLevel = baseLevel;
        character.level = level;
        character.isEnemy = isEnemy;
        if (skills != null) character.skills = skills.ToCharacterSkills();
        if (stats != null) character.stats = stats.ToCharacterStatus();
        if (mods != null) character.mods = mods.ToCharacterMods();
        if (ailDefs != null) character.ailDefs = ailDefs.ToCharacterAilmentDefenses();
        return character;
    }
}


[Serializable]
public class JsonPartyCharacters
{
    public string name;
    public string[] activeCharacterIds; // Store character IDs instead of references
    public string[] stockCharacterIds;

    public JsonPartyCharacters() { }

    public JsonPartyCharacters(string partyName)
    {
        name = partyName;
        activeCharacterIds = new string[4] { null, null, null, null }; // Empty active slots
        stockCharacterIds = new string[12]; // Empty stock slots (default to null)
        for (int i = 0; i < stockCharacterIds.Length; i++)
        {
            stockCharacterIds[i] = null; // null means empty slot
        }
    }

    public JsonPartyCharacters(PartyCharacters party)
    {
        name = party.name;
        activeCharacterIds = new string[party.activeCharacters.Length];
        stockCharacterIds = new string[party.stockCharacters.Length];

        for (int i = 0; i < party.activeCharacters.Length; i++)
        {
            activeCharacterIds[i] = party.activeCharacters[i]?.id;
        }

        for (int i = 0; i < party.stockCharacters.Length; i++)
        {
            stockCharacterIds[i] = party.stockCharacters[i]?.id;
        }
    }

    public PartyCharacters ToPartyCharacters(List<BattleCharacter> allCharacters)
    {
        BattleCharacter[] activeChars = new BattleCharacter[activeCharacterIds.Length];
        BattleCharacter[] stockChars = new BattleCharacter[stockCharacterIds.Length];

        for (int i = 0; i < activeCharacterIds.Length; i++)
        {
            if (!string.IsNullOrEmpty(activeCharacterIds[i]))
            {
                activeChars[i] = allCharacters.Find(c => c.id == activeCharacterIds[i]);
            }
        }

        for (int i = 0; i < stockCharacterIds.Length; i++)
        {
            if (!string.IsNullOrEmpty(stockCharacterIds[i]))
            {
                stockChars[i] = allCharacters.Find(c => c.id == stockCharacterIds[i]);
            }
        }

        return new PartyCharacters(name, activeChars, stockChars);
    }
}

// Container classes for different save files
[Serializable]
public class JsonVolumeSettings
{
    public float mainVolume;
    public float musicVolume;
    public float sfxVolume;
    public bool musicEnabled;
    public bool sfxEnabled;

    public JsonVolumeSettings() { }
}

[Serializable]
public class JsonDamageVariables
{
    public float B, C, D, E, F, G;
    public float Difficulty;
    public float StockWeight;
    public float MaxPP;
    public float ReductionPerCombo;
    public float FractionOfAttacksPerAction;
    public int ModFraction;
    public float IDBOffset;
    public bool infinity;
    public bool changeOnlyNulls;
    public bool showHP;
    public bool slotNames;

    public JsonDamageVariables() { }
}

[Serializable]
public class JsonComboChances
{
    public int[] values;

    public JsonComboChances() { }
}

[Serializable]
public class JsonGoldenStrike
{
    public int[] values;

    public JsonGoldenStrike() { }
}

[Serializable]
public class JsonEnergyAmount
{
    public int energyTimer;

    public JsonEnergyAmount() { }
}


[Serializable]
public class JsonAIConfigs
{
    public float epsilon;
    public float beta;
    public float Nt;
    public float theta;
    public float kappa;
    public int NStochastic;

    public JsonAIConfigs() { }
}
