using UnityEngine;
using UnityEngine.UI;

public class SwitchConfigs : MonoBehaviour
{
    public GameObject CharArea;
    public GameObject AIArea;
    public GameObject WeaponsArea;
    public GameObject PartyArea;
    public GameObject ResurrectArea;

    public GameObject StickyCopy;

    GameObject CharButton;
    GameObject AIButton;
    GameObject WeaponsButton;
    GameObject PartyButton;
    GameObject ResurrectButton;

    GameObject CharConfigs;
    GameObject AIConfigs;
    GameObject WeaponsConfigs;
    GameObject PartyConfigs;
    GameObject ResurrectConfigs;

    public GameObject GamePlayConfigs;

    public PartyConfigs partyConfigs;
    public CharResContainer charResContainer;

    StickyUIElement stickyUIElement;

    void Start()
    {

        stickyUIElement = GameObject.Find("PartyArea").GetComponent<StickyUIElement>();

        CharButton = CharArea.transform.GetChild(0).gameObject;
        AIButton = AIArea.transform.GetChild(0).gameObject;
        WeaponsButton = WeaponsArea.transform.GetChild(0).gameObject;
        PartyButton = PartyArea.transform.GetChild(0).gameObject;
        ResurrectButton = ResurrectArea.transform.GetChild(0).gameObject;

        CharConfigs = CharArea.transform.GetChild(1).gameObject;
        AIConfigs = AIArea.transform.GetChild(1).gameObject;
        WeaponsConfigs = WeaponsArea.transform.GetChild(1).gameObject;
        PartyConfigs = PartyArea.transform.GetChild(1).gameObject;
        ResurrectConfigs = ResurrectArea.transform.GetChild(1).gameObject;

        CharButton.GetComponent<Button>().onClick.AddListener(() => CharHandler());
        AIButton.GetComponent<Button>().onClick.AddListener(() => AIHandler());
        WeaponsButton.GetComponent<Button>().onClick.AddListener(() => WeaponsHandler());
        PartyButton.GetComponent<Button>().onClick.AddListener(() => PartyHandler());
        ResurrectButton.GetComponent<Button>().onClick.AddListener(() => ResurrectHandler());
    }

    private void CharHandler()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        bool isActive = CharConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(true);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(false);
        }
    }

    private void AIHandler()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        bool isActive = AIConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(true);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(false);
        }
    }

    private void WeaponsHandler()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        bool isActive = WeaponsConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(true);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(false);
        }
    }

    private void PartyHandler()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        bool isActive = PartyConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(true);
            ResurrectConfigs.SetActive(false);
            GamePlayConfigs.SetActive(false);

            if (stickyUIElement.isStuck) StickyCopy.SetActive(true);
            // Refreshes the UI of the party
            //partyConfigs.RefreshPartyUI();
        }


    }

    private void ResurrectHandler()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        bool isActive = ResurrectConfigs.activeSelf;

        if (isActive)
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            GamePlayConfigs.SetActive(true);
        }
        else
        {
            CharConfigs.SetActive(false);
            AIConfigs.SetActive(false);
            WeaponsConfigs.SetActive(false);
            PartyConfigs.SetActive(false);
            ResurrectConfigs.SetActive(true);
            GamePlayConfigs.SetActive(false);
            StickyCopy.SetActive(false);
            // Refreshes the UI of the party
            charResContainer.RefreshCharResUI();
        }
    }
}
