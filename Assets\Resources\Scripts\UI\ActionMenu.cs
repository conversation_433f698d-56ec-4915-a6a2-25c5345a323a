using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using UnityEngine.EventSystems;

public class ActionMenu : MonoBehaviour
{
    // GameObjects that are used for the action menu UI
    public GameObject skills, items, guard, weapon, change, escape;

    readonly Dictionary<Button, Color> buttons = new();

    Camera mainCamera;

    // ConfigsHandler reference
    ConfigsHandler configsHandler;
    public CameraEffect cameraEffect;

    public bool active = true;

    void Start()
    {
        // gets the configs handler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // gets the main camera
        mainCamera = GameObject.Find("UIOverlayCam").GetComponent<Camera>();

        buttons.Add(skills.GetComponent<Button>(), skills.GetComponent<Image>().color);
        skills.GetComponent<Image>().color = Color.white;

        buttons.Add(weapon.GetComponent<Button>(), weapon.GetComponent<Image>().color);
        weapon.GetComponent<Image>().color = Color.white;

        buttons.Add(items.GetComponent<Button>(), items.GetComponent<Image>().color);
        items.GetComponent<Image>().color = Color.white;

        buttons.Add(guard.GetComponent<Button>(), guard.GetComponent<Image>().color);
        guard.GetComponent<Image>().color = Color.white;

        buttons.Add(change.GetComponent<Button>(), change.GetComponent<Image>().color);
        change.GetComponent<Image>().color = Color.white;

        buttons.Add(escape.GetComponent<Button>(), escape.GetComponent<Image>().color);
        escape.GetComponent<Image>().color = Color.white;


        // activates or deactivates the skills menu, deactivates the change menu and starts the animation
        skills.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            skills.transform.GetChild(1).gameObject.SetActive(!skills.transform.GetChild(1).gameObject.activeSelf);
            change.transform.GetChild(1).gameObject.SetActive(false);
            StartCoroutine(ButtonAnimation(skills));
        });
        skills.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_SKILLS");

        items.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            StartCoroutine(ButtonAnimation(items));
        });
        items.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_POWER-UPS");

        // makes the player guard and hides the action menu and everything else that could be open
        guard.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            configsHandler.GuardCharacter();
            skills.transform.GetChild(1).gameObject.SetActive(false);
            change.transform.GetChild(1).gameObject.SetActive(false);
            gameObject.SetActive(false);
        });
        guard.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_GUARD");

        weapon.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            StartCoroutine(ButtonAnimation(weapon));
        });
        weapon.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_WEAPONS");

        // hides the skill menu and show or hide the change menu
        change.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            skills.transform.GetChild(1).gameObject.SetActive(false);
            change.transform.GetChild(1).gameObject.SetActive(true);
            StartCoroutine(ButtonAnimation(change));
        });
        change.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_CHANGE");

        // hides everything open of the action menu and show the escape popup
        escape.GetComponent<Button>().onClick.AddListener(() =>
        {
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
            skills.transform.GetChild(1).gameObject.SetActive(false);
            change.transform.GetChild(1).gameObject.SetActive(false);
            configsHandler.EscapePopup.SetActive(true);
            configsHandler.EscapePopup.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = "The chance of success is " + configsHandler.ChanceOfEscape() + "%";
            StartCoroutine(ButtonAnimation(escape));
        });
        escape.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("BATTLE_MENU_ESCAPE");
    }

    void Update()
    {
        // this is here because the menu can be open even when the player is dead, or doesn't exist
        // so this just disables the buttons that can't be used
        skills.GetComponent<Button>().interactable = configsHandler.IsSelectedPlayerAvalable() && configsHandler.playerActions > 0 ;
        items.GetComponent<Button>().interactable = configsHandler.IsSelectedPlayerAvalable() && configsHandler.playerActions > 0;
        guard.GetComponent<Button>().interactable = configsHandler.IsSelectedPlayerAvalable() && !configsHandler.IsGuarding() && configsHandler.playerActions > 0;
        weapon.GetComponent<Button>().interactable = configsHandler.IsSelectedPlayerAvalable() && configsHandler.playerActions > 0;
        change.GetComponent<Button>().interactable = configsHandler.playerActions > 0;
        escape.GetComponent<Button>().interactable = configsHandler.IsSelectedPlayerAvalable() && configsHandler.playerActions > 0;

        // changes the color of the buttons, because im using sprite change for the buttons
        foreach (var item in buttons) UpdateButtonColor(item.Key, item.Value);


        if (Input.touchCount > 0) // it just hides the action menu if the player doesn't click on it
        {
            if (Input.GetTouch(0).phase == TouchPhase.Began) active = false;

            if (!active && Input.GetTouch(0).phase == TouchPhase.Ended && !IsPointerOverMenu())
            {
                skills.transform.GetChild(1).gameObject.SetActive(false);
                change.transform.GetChild(1).gameObject.SetActive(false);
                gameObject.SetActive(false);
                active = true;
                cameraEffect.ResetCamera();
            }
        }

    }

    static void UpdateButtonColor(Button button, Color color)
    {
        Image buttonImage = button.GetComponent<Image>();

        if(button.interactable)
        {
            if (EventSystem.current.currentSelectedGameObject == button.gameObject) buttonImage.color = color;
            else buttonImage.color = Color.white;
        }
        else buttonImage.color = new Color(0.3f, 0.3f, 0.3f, 1);

        button.transform.GetChild(0).GetComponent<TextMeshProUGUI>().color = (buttonImage.color != color) ? Color.black : color;
    }

    bool IsPointerOverMenu() // Checks if the cursor is over the menu
    {
        Vector2 cursorPos = Input.GetTouch(0).position;

        var selfRect = GetComponent<RectTransform>();
        var changeChild1 = change.transform.GetChild(1).gameObject;

        bool overSelf = RectTransformUtility.RectangleContainsScreenPoint(selfRect, cursorPos, mainCamera);

        bool overChild1 = changeChild1.activeInHierarchy
            && RectTransformUtility.RectangleContainsScreenPoint(
                changeChild1.GetComponent<RectTransform>(), cursorPos, mainCamera);

        return overSelf || overChild1;
    }

    IEnumerator ButtonAnimation(GameObject button) // animates the buttons by making them half size and then back
    {
        button.GetComponent<RectTransform>().sizeDelta /= 2;
        button.transform.GetChild(0).localScale /= 2;

        yield return new WaitForSeconds(0.05f);
        button.GetComponent<RectTransform>().sizeDelta *= 2;
        button.transform.GetChild(0).localScale *= 2;
    }
}
