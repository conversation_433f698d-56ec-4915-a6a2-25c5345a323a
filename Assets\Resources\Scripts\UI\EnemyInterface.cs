using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class EnemyInterface : MonoBehaviour
{
    public GameObject ConfigHandlerOBJ; // Reference to the ConfigsHandler GameObject
    public GameObject Button; // Reference to the Button GameObject to show the values

    public Image AttackCooldown; // Reference to the Image to show the attack cooldown
    Image delayedHealthBar;

    public bool didCrit = false;

    public GameObject valueOBJ; // Reference to the EnemyValues GameObject


    public BattleCharacter eC;

    EnemyAnimation enemyAnim;
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler script
    Button button; // Reference to the Button component

    private readonly float animationDuration = 0.5f;
    private Tween delayTween;

    private float lastClickTime = 0f;
    private readonly float doubleClickThreshold = 0.3f;

    int enemyIndex; // Index of the enemy

    void Start()
    {
        enemyAnim = GetComponent<EnemyAnimation>();

        if (valueOBJ.activeSelf) valueOBJ.SetActive(false); // Set the EnemyValues GameObject to inactive

        enemyIndex = int.Parse(name[^1..]); // Get the index of the enemy

        configsHandler = ConfigHandlerOBJ.GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        button = Button.GetComponent<Button>(); // Get the Button component

        delayedHealthBar = Button.transform.GetChild(2).GetChild(0).GetComponent<Image>();

        AttackCooldown.fillAmount = 0; // Set the attack cooldown to 0

        button.onClick.AddListener(() => // Add the listener to the button to show the values
        {
            float timeSinceLastClick = Time.time - lastClickTime;

            if (timeSinceLastClick <= doubleClickThreshold)
            {
                if (!valueOBJ.activeSelf)
                {
                    ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
                    ShowSkillValues();
                }
            }
            else
            {
                if (!valueOBJ.activeSelf)
                {
                    if (configsHandler.GetNumberOfEnemies() > 1)
                    {
                        // Single-click behavior: select the enemy
                        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
                        configsHandler.SetEnemySelected(enemyIndex);
                    }
                }
            }

            lastClickTime = Time.time;
        });

    }

    void ShowSkillValues()
    {
        valueOBJ.SetActive(true);
        valueOBJ.transform.position = new Vector3(0, 2.5f, 0);
        valueOBJ.GetComponent<EnemyValues>().UpdateCharacter(configsHandler.GetEnemyCharacter(enemyIndex));
    }

    void Update()
    {
        eC = configsHandler.GetEnemyCharacter(enemyIndex); // Get the enemy character

        if (eC != null && !eC.IsDead) // If the enemy is not dead it updates the values
        {

            transform.GetChild(0).gameObject.SetActive(true); // Set the EnemyInterface GameObject to active

            // Reduce the attack cooldown of the enemy. Speed of 0 takes 10 seconds,
            // speed of 200 takes 1 second, scaling linearly between those values
            if (AttackCooldown.fillAmount > 0.0001f)
            {
                float speed = Mathf.Max(0, eC.mods.GetSpeed());
                float duration = Mathf.Lerp(10f, 1f, Mathf.Clamp01(speed / 200f));
                // Defensive clamp to avoid extreme durations if speed ever goes out of expected range
                duration = Mathf.Clamp(duration, 0.1f, 20f);
                AttackCooldown.fillAmount -= Time.deltaTime / duration;
            }


            // Updates the health bar of the enemy, reducing it smoothly to the current health
            Button.transform
                .GetChild(2)
                .GetChild(1)
                .GetComponent<Image>().fillAmount = (eC.maxHP == 0) ? 1 :
                    Mathf.MoveTowards(Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount, (float)eC.hP / eC.maxHP, Time.deltaTime);

            float targetFillAmount = (eC.maxHP == 0) ? 1 : (float)eC.hP / eC.maxHP;
            UpdateHealthBarDelay(targetFillAmount); // delay for the health bar

            Button.transform
                .GetChild(2)
                .GetChild(1)
                .GetComponent<Image>().color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount,
                    5f);

            // Updates the text of the health bar to show the current health and the max health,
            // this is still here if you want to show the health of the enemy, for that
            // use eC.hP.ToString() + "/" + eC.maxHP.ToString() instead of "???"
            GameObject healthbar =
            Button.transform
                .GetChild(2)
                .GetChild(2).gameObject;

            TextMeshProUGUI healthbartext = healthbar.transform
                .GetChild(0)
                .GetComponent<TextMeshProUGUI>();

            healthbartext.text = configsHandler.ShowHP.GetComponent<Toggle>().isOn ? eC.hP.ToString() + "/" + eC.maxHP.ToString() : "???";

            float size = healthbartext.GetPreferredValues().x + 0.19f;

            healthbar.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, size);

            // Updates the label that contains the enemy's name
            Button.transform
                .GetChild(3)
                .GetComponent<TextMeshProUGUI>().text = eC.name;

            Button.transform
                .GetChild(5)
                .gameObject.SetActive(eC.isStunned);
        }
        else // else it removes the cooldown and hides it
        {
            if (AttackCooldown.fillAmount > 0.0001f) AttackCooldown.fillAmount = 0;
            Button.SetActive(false);
        }

        // shows the selected highlight if the enemy is selected
        if (configsHandler.GetNumberOfEnemies() > 1)
        {
            Button.transform.GetChild(1).gameObject.SetActive(configsHandler.GetEnemySelected(enemyIndex));
        }
        if (configsHandler.GetNumberOfEnemies() == 1)
        {
            Button.transform.GetChild(1).gameObject.SetActive(false);
        }

        DetectOutsideTapToHideValues();

    }

    void UpdateHealthBarDelay(float targetFillAmount)
    {
        // Kill existing tween to prevent overlapping
        delayTween?.Kill();

        // Start smooth animation with ease-in
        delayTween = delayedHealthBar.DOFillAmount(targetFillAmount, animationDuration);
    }

    public bool IsHPDepliding()
    {
        if (eC == null) return false;

        float healthBar = Button.transform
                        .GetChild(2)
                        .GetChild(1)
                        .GetComponent<Image>().fillAmount;

        float healthAmount = (float)eC.hP / eC.maxHP;

        return healthBar > healthAmount || enemyAnim.gotHitted;
    }

    bool IsTouchOverGameObject(GameObject obj)
    {
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = Input.GetTouch(0).position
        };

        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);

        foreach (var result in results)
        {
            if (result.gameObject == obj || result.gameObject.transform.IsChildOf(obj.transform))
                return true;
        }
        return false;
    }

    void DetectOutsideTapToHideValues()
    {
        if (Input.touchCount == 0) return;

        Touch touch = Input.GetTouch(0);

        // Only act on touch begin to avoid multiple triggers
        if (touch.phase != TouchPhase.Began) return;

        if (valueOBJ.activeSelf && !IsTouchOverGameObject(valueOBJ))
        {
            valueOBJ.SetActive(false);
        }
    }

}
