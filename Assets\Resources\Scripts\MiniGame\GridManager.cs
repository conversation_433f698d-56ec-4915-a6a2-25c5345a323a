﻿using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Text;
using UnityEngine.EventSystems;
using System.Linq;



public class GridManager : MonoBehaviour
{
    //public GameObject runePrefab; // the rune prefab
    public GameObject TileBackground;

    private readonly int width = 6, height = 5;

    // Cache enum values to avoid repeated Enum.GetValues calls
    private static readonly Types[] typesArray = (Types[])Enum.GetValues(typeof(Types));
    private static readonly int typesCount = typesArray.Length;

    public Rune[,] runes;
    public ConfigsHandler configsHandler;
    public AIConfs aiConfs;
    public RunePool runePool;
    BattleCharacter[] enemyBattleCharacters;
    BattleCharacter[] playerBattleCharacters;
    public BattleCharacter enemy;
    public BattleCharacter player;
    int enemyIndex;
    int playerIndex;

    public bool canCheckMatches = true; // eli
    public bool canDestroy = true; //eli

    public int attacks = 0;
    public int combo = 0;

    [SerializeField] private GridLayoutGroup gridLayoutGroup;
    private readonly List<Vector3> cachedLocalPositions = new List<Vector3>();

    // Cache audio clips and components to avoid repeated loading/lookups
    private AudioClip runeSwitchClip, runeClearClip;
    private AudioSource cachedAudioSource;

    public float swapCooldown = 0.05f;
    //private float lastSwapTime = -1f;

    public bool isBoardBusy = false;
    public bool collapseEnded = true; // eli
    public bool isAttacking = false;

    bool canMoveBackground = true;
    public bool didTurnsSwitch = true;
    bool canPlayerPlay = true;
    public bool playerTurn = true;
    bool NotvalidPlayerSelected = true;

    private Dictionary<Types, Sprite> runeSprites;

    // Cache frequently used collections to reduce GC pressure
    private readonly HashSet<Rune> matchedRunes = new HashSet<Rune>();
    private readonly List<Vector2Int> emptySpotsList = new List<Vector2Int>();
    private readonly HashSet<Vector2Int> forcedPositionsSet = new HashSet<Vector2Int>();

    // Cache for expensive method call results
    private bool cachedIsThereCharacters;
    private bool cachedIsPPLeft;
    private bool cachedIsThereAnyPlayerNotInCooldown;
    private BattleCharacter cachedPlayerCharacter;
    private float lastCacheUpdateTime = -1f;
    private const float CACHE_UPDATE_INTERVAL = 0.1f; // Update cache every 100ms

    // StringBuilder for efficient string operations
    private readonly StringBuilder stringBuilder = new StringBuilder(32);

    // Cache direction vectors to avoid repeated allocations
    private static readonly Vector2Int[] directions = new Vector2Int[]
    {
        new Vector2Int(0, 1),    // Up
        new Vector2Int(0, -1),   // Down
        new Vector2Int(-1, 0),   // Left
        new Vector2Int(1, 0),    // Right
        new Vector2Int(-1, 1),   // Top-left
        new Vector2Int(1, 1),    // Top-right
        new Vector2Int(-1, -1),  // Bottom-left
        new Vector2Int(1, -1)    // Bottom-right
    };

    // Grid boundary cache for extended swapping
    private Rect gridBounds;
    private bool gridBoundsCalculated = false;

    public GraphicRaycaster raycaster;
    public EventSystem eventSystem;

    private Queue<(Rune dragged, Rune target)> swapQueue = new Queue<(Rune, Rune)>(); // Queue for runes swaps
    private bool isSwapping = false;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Cache expensive GameObject.Find calls
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        runePool = GetComponent<RunePool>();
        TileBackground = GameObject.Find("TileBackground");
        gridLayoutGroup = GetComponent<GridLayoutGroup>();

        // Cache AudioSource component to avoid repeated GetComponent calls
        cachedAudioSource = configsHandler.GetComponent<AudioSource>();

        // Initialize sprite dictionary using cached types array
        runeSprites = new Dictionary<Types, Sprite>(typesCount);
        foreach (Types t in typesArray)
        {
            runeSprites[t] = Resources.Load<Sprite>($@"Sprites/UI/{t}");
        }

        // Pre-load audio clips to avoid repeated Resources.Load calls
        runeSwitchClip = Resources.Load<AudioClip>("SFX/RuneSwitch");
        runeClearClip = Resources.Load<AudioClip>("SFX/RuneClear");

        GenerateGrid();

        // Initialize cache
        UpdateMethodCallCache();
    }

    /// <summary>
    /// Updates cached method call results to reduce expensive repeated calls
    /// </summary>
    private void UpdateMethodCallCache()
    {
        cachedIsThereCharacters = configsHandler.IsThereCharacters();
        cachedIsPPLeft = configsHandler.IsPPLeft();
        cachedIsThereAnyPlayerNotInCooldown = configsHandler.IsThereAnyPlayerNotInCooldown();
        cachedPlayerCharacter = configsHandler.GetPlayerCharacter(configsHandler.GetSelectedPlayer());
        lastCacheUpdateTime = Time.time;
    }

    /// <summary>
    /// Checks if cache needs updating and updates if necessary
    /// </summary>
    private void CheckAndUpdateCache()
    {
        if (Time.time - lastCacheUpdateTime > CACHE_UPDATE_INTERVAL)
        {
            UpdateMethodCallCache();
        }
    }

    // Update is called once per frame
    void Update()
    {
        //Debug.Log("parry count: " + configsHandler.parriedAttack);
        CheckAndUpdateCache();
        HandleTileBackgroundMovement();

        // Always gets the enemy selected
        enemyIndex = configsHandler.GetSelectedEnemy();
        enemyBattleCharacters = configsHandler.eC;

        if (enemyBattleCharacters != null &&
            enemyIndex >= 0 &&
            enemyIndex < enemyBattleCharacters.Count() &&
            enemyBattleCharacters[enemyIndex] != null)
        {
            enemy = enemyBattleCharacters[enemyIndex];
        }
        else
        {
            enemy = null;
        }

        // Always gets the attacked player
        playerIndex = configsHandler.attackedPlayer;
        playerBattleCharacters = configsHandler.pC;

        if (playerBattleCharacters != null &&
            playerIndex >= 0 &&
            playerIndex < playerBattleCharacters.Count() &&
            playerBattleCharacters[playerIndex] != null)
        {
            player = playerBattleCharacters[playerIndex];
        }
        else
        {
            player = null;
        }
    }

    private void HandleTileBackgroundMovement()
    {
        // Move the Tile background forwards and backwards depending who the turn is
        // Use cached values to reduce expensive method calls
        bool shouldMoveToBack = (!didTurnsSwitch && canMoveBackground)
            || (canPlayerPlay && !cachedIsThereAnyPlayerNotInCooldown)
            || (!NotvalidPlayerSelected && cachedPlayerCharacter == null)
            || !configsHandler.playerTurn;

        if (shouldMoveToBack)
        {
            TileBackground.transform.SetAsLastSibling();
            if (!didTurnsSwitch && canMoveBackground) canMoveBackground = false;
            if (canPlayerPlay && !cachedIsThereAnyPlayerNotInCooldown) canPlayerPlay = false;
            if (!NotvalidPlayerSelected && cachedPlayerCharacter == null) NotvalidPlayerSelected = true;
        }

        bool shouldMoveToFront = ((didTurnsSwitch && !canMoveBackground)
            || (!canPlayerPlay && cachedIsThereAnyPlayerNotInCooldown)
            || (NotvalidPlayerSelected && cachedPlayerCharacter != null)
            || configsHandler.playerTurn)
            && cachedIsPPLeft
            && cachedPlayerCharacter != null;

        if (shouldMoveToFront)
        {
            TileBackground.transform.SetAsFirstSibling();
            if (didTurnsSwitch && !canMoveBackground) canMoveBackground = true;
            if (!canPlayerPlay && cachedIsThereAnyPlayerNotInCooldown) canPlayerPlay = true;
            if (NotvalidPlayerSelected && cachedPlayerCharacter != null) NotvalidPlayerSelected = false;
        }
    }

    void GenerateGrid() // Create grid with gridLayoutGroup
    {
        runes = new Rune[width, height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                GameObject runeObj = runePool.GetRune();

                Types type = (Types)UnityEngine.Random.Range(0, typesCount);

                // Safely set the rune sprite with null checks
                //SetRuneSprite(runeObj, type, "in GenerateGrid");
                runeObj.GetComponent<Image>().sprite = runeSprites[type];
                runeObj.transform.GetChild(0).GetChild(0).GetComponent<Image>().sprite = runeSprites[type];

                Rune rune = runeObj.GetComponent<Rune>();
                rune.Initialize(x, y, this);

                runes[x, y] = rune;
                rune.type = type;

                // Use StringBuilder for efficient string concatenation
                stringBuilder.Clear();
                stringBuilder.Append(type.ToString());
                stringBuilder.Append("Rune (x");
                stringBuilder.Append(x);
                stringBuilder.Append(", y");
                stringBuilder.Append(y);
                stringBuilder.Append(")");
                rune.name = stringBuilder.ToString();

            }
        }
        StartCoroutine(GenerateAndCheckInitialMatches()); // Check Matches after grid generation
    }

    IEnumerator FinalizeGridLayout()
    {
        // Wait for layout to complete
        yield return new WaitForEndOfFrame();

        // Cache local positions
        cachedLocalPositions.Clear();
        foreach (Transform child in transform)
        {
            cachedLocalPositions.Add(child.localPosition);
        }

        // Disable GridLayoutGroup so we can animate freely
        gridLayoutGroup.enabled = false;

        // Reapply positions (to "bake in" the layout)
        int i = 0;
        foreach (Transform child in transform)
        {
            child.localPosition = cachedLocalPositions[i];
            i++;
        }

        // Reset grid bounds calculation flag so it gets recalculated with new positions
        gridBoundsCalculated = false;
    }

    IEnumerator GenerateAndCheckInitialMatches()
    {
        yield return new WaitForSeconds(0.2f);
        yield return StartCoroutine(FinalizeGridLayout());
        yield return new WaitForSeconds(0.15f); // Allow layout to settle
        StartCoroutine(CheckAndClearMatchesSequence());
    }

    IEnumerator CheckAndClearMatchesSequence() // Ches for matches at grid formation
    {
        yield return new WaitForSeconds(0.15f);
        yield return RuneMatching();
        //yield return new WaitForSeconds(0.2f);
        yield return StartCoroutine(CollapseRunes());
        //yield return new WaitForSeconds(0.25f);
        yield return StartCoroutine(SpawnNewRunes());
        //yield return new WaitForSeconds(0.35f);

        // Keep looping until no more matches are found
        while (HasMatches())
        {
            yield return new WaitForSeconds(0.15f);
            yield return RuneMatching();
            //yield return new WaitForSeconds(0.2f);
            yield return StartCoroutine(CollapseRunes());
            //yield return new WaitForSeconds(0.25f);
            yield return StartCoroutine(SpawnNewRunes());
            //yield return new WaitForSeconds(0.25f);
        }

        canCheckMatches = true;
    }

    bool HasMatches() // Check for matches at grid formation
    {
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width - 2; x++)
            {
                if (runes[x, y] != null && runes[x + 1, y] != null && runes[x + 2, y] != null &&
                    runes[x, y].type == runes[x + 1, y].type &&
                    runes[x, y].type == runes[x + 2, y].type)
                {
                    return true;
                }
            }
        }

        for (int y = 0; y < height - 2; y++)
        {
            for (int x = 0; x < width; x++)
            {
                if (runes[x, y] != null && runes[x, y + 1] != null && runes[x, y + 2] != null &&
                    runes[x, y].type == runes[x, y + 1].type &&
                    runes[x, y].type == runes[x, y + 2].type)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Calculate and cache the grid boundaries for extended swapping
    /// </summary>
    public void CalculateGridBounds()
    {
        if (gridBoundsCalculated || cachedLocalPositions.Count == 0) return;

        // Get the bounds from the first and last rune positions
        Vector3 topLeft = cachedLocalPositions[0]; // First position (0,0)
        Vector3 bottomRight = cachedLocalPositions[cachedLocalPositions.Count - 1]; // Last position

        // Convert to world positions
        Vector3 worldTopLeft = transform.TransformPoint(topLeft);
        Vector3 worldBottomRight = transform.TransformPoint(bottomRight);

        // Create bounds with some padding for the rune size
        float padding = 0.5f; // Adjust based on rune size
        gridBounds = new Rect(
            worldTopLeft.x - padding,
            worldBottomRight.y - padding,
            worldBottomRight.x - worldTopLeft.x + 2 * padding,
            worldTopLeft.y - worldBottomRight.y + 2 * padding
        );

        gridBoundsCalculated = true;
    }

    /// <summary>
    /// Check if a world position is outside the grid boundaries and return the mapped edge position
    /// </summary>
    /// <param name="worldPos">World position to check</param>
    /// <param name="originalRune">The rune being dragged</param>
    /// <param name="mappedX">Mapped X coordinate (column)</param>
    /// <param name="mappedY">Mapped Y coordinate (row)</param>
    /// <returns>True if position is outside grid bounds</returns>
    private bool IsOutsideGridAndGetMappedPosition(Vector2 worldPos, Rune originalRune, out int mappedX, out int mappedY)
    {
        CalculateGridBounds();

        mappedX = originalRune.x;
        mappedY = originalRune.y;
        bool isOutside = false;

        // Check vertical boundaries (above/below grid)
        if (worldPos.y > gridBounds.yMax)
        {
            // Above grid - map to first row (y = 0)
            mappedY = 0;
            isOutside = true;
        }
        else if (worldPos.y < gridBounds.yMin)
        {
            // Below grid - map to last row
            mappedY = height - 1;
            isOutside = true;
        }

        // Check horizontal boundaries (left/right of grid)
        if (worldPos.x < gridBounds.xMin)
        {
            // Left of grid - map to first column (x = 0)
            mappedX = 0;
            isOutside = true;
        }
        else if (worldPos.x > gridBounds.xMax)
        {
            // Right of grid - map to last column
            mappedX = width - 1;
            isOutside = true;
        }

        // If outside vertically, map horizontal position within the row
        if (isOutside && (worldPos.y > gridBounds.yMax || worldPos.y < gridBounds.yMin))
        {
            // Map horizontal position to column within the mapped row
            float normalizedX = Mathf.InverseLerp(gridBounds.xMin, gridBounds.xMax, worldPos.x);
            mappedX = Mathf.Clamp(Mathf.RoundToInt(normalizedX * (width - 1)), 0, width - 1);
        }

        // If outside horizontally, map vertical position within the column
        if (isOutside && (worldPos.x < gridBounds.xMin || worldPos.x > gridBounds.xMax))
        {
            // Map vertical position to row within the mapped column
            float normalizedY = Mathf.InverseLerp(gridBounds.yMin, gridBounds.yMax, worldPos.y);
            mappedY = Mathf.Clamp(Mathf.RoundToInt((1f - normalizedY) * (height - 1)), 0, height - 1);
        }

        return isOutside;
    }

    public Rune GetNearestAdjacentRune(Rune draggedRune) // Not used right now, if new swap system gets aproved eliminate it
    {
        Vector2 cursorPos = Input.mousePosition;
        Rune hoveredRune = GetRuneUnderCursor(cursorPos);

        // Prioritize exact image-based match
        if (hoveredRune != null && hoveredRune != draggedRune && IsAdjacent(draggedRune.x, draggedRune.y, hoveredRune.x, hoveredRune.y))
        {
            return hoveredRune;
        }

        // Fallback: allow out-of-bounds mapping
        Vector2 worldPos = Camera.main.ScreenToWorldPoint(cursorPos);
        if (IsOutsideGridAndGetMappedPosition(worldPos, draggedRune, out int mappedX, out int mappedY))
        {
            return GetCursorBasedSwapTarget(draggedRune, worldPos);
        }

        return null;
    }

    private Rune GetRuneUnderCursor(Vector2 screenPos)
    {
        PointerEventData pointerData = new PointerEventData(eventSystem)
        {
            position = screenPos
        };

        List<RaycastResult> results = new List<RaycastResult>();
        raycaster.Raycast(pointerData, results);

        foreach (RaycastResult result in results)
        {
            Rune rune = result.gameObject.GetComponent<Rune>();
            if (rune != null)
            {
                return rune;
            }
        }

        return null;
    }

    /// <summary>
    /// Get the target rune for extended swapping based on cursor position when dragged rune is outside grid boundaries
    /// </summary>
    /// <param name="draggedRune">The rune being dragged</param>
    /// <param name="cursorPos">Current cursor position</param>
    /// <returns>Target rune for swapping, or null if no valid target</returns>
    private Rune GetCursorBasedSwapTarget(Rune draggedRune, Vector2 cursorPos)
    {
        CalculateGridBounds();

        // Determine if we're outside vertically or horizontally
        bool outsideVertically = cursorPos.y > gridBounds.yMax || cursorPos.y < gridBounds.yMin;
        bool outsideHorizontally = cursorPos.x < gridBounds.xMin || cursorPos.x > gridBounds.xMax;

        if (outsideVertically)
        {
            // Determine which row to map to based on vertical position
            int targetRow;
            if (cursorPos.y > gridBounds.yMax)
            {
                targetRow = 0; // Above grid - map to first row
            }
            else
            {
                targetRow = height - 1; // Below grid - map to last row
            }

            // Use cursor's X-coordinate to determine which column to target
            int targetColumn = GetColumnFromCursorX(cursorPos.x);

            // Get the target rune at the calculated position
            if (targetColumn >= 0 && targetColumn < width && targetRow >= 0 && targetRow < height)
            {
                Rune targetRune = runes[targetColumn, targetRow];
                if (targetRune != null && targetRune != draggedRune)
                {
                    return targetRune;
                }
            }
        }
        else if (outsideHorizontally)
        {
            // Determine which column to map to based on horizontal position
            int targetColumn;
            if (cursorPos.x < gridBounds.xMin)
            {
                targetColumn = 0; // Left of grid - map to first column
            }
            else
            {
                targetColumn = width - 1; // Right of grid - map to last column
            }

            // Use cursor's Y-coordinate to determine which row to target
            int targetRow = GetRowFromCursorY(cursorPos.y);

            // Get the target rune at the calculated position
            if (targetColumn >= 0 && targetColumn < width && targetRow >= 0 && targetRow < height)
            {
                Rune targetRune = runes[targetColumn, targetRow];
                if (targetRune != null && targetRune != draggedRune)
                {
                    return targetRune;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Check if two grid positions are adjacent (including diagonally)
    /// </summary>
    /// <param name="x1">First position X coordinate</param>
    /// <param name="y1">First position Y coordinate</param>
    /// <param name="x2">Second position X coordinate</param>
    /// <param name="y2">Second position Y coordinate</param>
    /// <returns>True if positions are adjacent</returns>
    private bool IsAdjacent(int x1, int y1, int x2, int y2)
    {
        int deltaX = Mathf.Abs(x2 - x1);
        int deltaY = Mathf.Abs(y2 - y1);

        // Adjacent means difference of at most 1 in both X and Y, but not both 0
        return (deltaX <= 1 && deltaY <= 1) && !(deltaX == 0 && deltaY == 0);
    }

    /// <summary>
    /// Calculate which column the cursor X position corresponds to
    /// </summary>
    /// <param name="cursorX">Cursor X position in world space</param>
    /// <returns>Column index (0 to width-1)</returns>
    private int GetColumnFromCursorX(float cursorX)
    {
        CalculateGridBounds();

        // Normalize cursor X position within grid bounds
        float normalizedX = Mathf.InverseLerp(gridBounds.xMin, gridBounds.xMax, cursorX);

        // Map to column index
        int column = Mathf.FloorToInt(normalizedX * width);

        // Clamp to valid range
        column = Mathf.Clamp(column, 0, width - 1);

        return column;
    }

    /// <summary>
    /// Calculate which row the cursor Y position corresponds to
    /// </summary>
    /// <param name="cursorY">Cursor Y position in world space</param>
    /// <returns>Row index (0 to height-1)</returns>
    private int GetRowFromCursorY(float cursorY)
    {
        CalculateGridBounds();

        // Normalize cursor Y position within grid bounds (inverted because Y increases upward)
        float normalizedY = Mathf.InverseLerp(gridBounds.yMin, gridBounds.yMax, cursorY);

        // Map to row index (inverted because row 0 is at the top)
        int row = Mathf.FloorToInt((1f - normalizedY) * height);

        // Clamp to valid range
        row = Mathf.Clamp(row, 0, height - 1);

        return row;
    }

    #region Swap Runes

    public Rune GetRuneAt(Vector2Int gridPos)
    {
        if (gridPos.x < 0 || gridPos.x >= width || gridPos.y < 0 || gridPos.y >= height)
            return null;

        return runes[gridPos.x, gridPos.y];
    }

    public Vector2Int WorldToGrid(Vector2 worldPos) // Calculate cursor position to grid position
    {
        int col = GetColumnFromCursorX(worldPos.x);
        int row = GetRowFromCursorY(worldPos.y);
        return new Vector2Int(col, row);
    }

    public void EnqueueSwap(Rune dragged, Rune target)
    {
        if (dragged == null || target == null || dragged == target) return;

        swapQueue.Enqueue((dragged, target));
        TryProcessNextSwap();
    }


    private void TryProcessNextSwap()
    {
        if (isSwapping || swapQueue.Count == 0) return;

        var (dragged, target) = swapQueue.Dequeue();
        StartCoroutine(ProcessSwap(dragged, target));
    }

    private IEnumerator ProcessSwap(Rune dragged, Rune target)
    {
        bool nextSwapStarted = false;
        isSwapping = true;

        dragged.isSwapping = true;
        target.isSwapping = true;

        // Cancel previous animations on target visual
        target.visual.DOKill();
        target.visual.localScale = Vector3.one;
        target.visual.localPosition = Vector3.zero;

        // Play sound and punch animation
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), runeSwitchClip);
        target.visual.DOPunchScale(Vector3.one * 0.4f, 0.2f, 10, 1f).SetEase(Ease.OutQuad);

        // Setup arc animation parameters
        Vector3 startPos = target.visual.position;
        Vector3 endPos = dragged.transform.position;
        Vector3 mid = (startPos + endPos) * 0.5f;
        Vector3 direction = (endPos - startPos).normalized;
        Vector3 perpendicular = Vector3.Cross(direction, Vector3.forward);
        float arcHeight = 0.7f;
        Vector3 arcPeak = mid + perpendicular * arcHeight;

        float baseDuration = 0.04f;
        float duration = baseDuration;


        // If there are swaps in queue, speed up animation to 0.01s
        int queueCount = swapQueue.Count;
        if (queueCount > 0)
        {
            duration = Mathf.Lerp(baseDuration, 0.01f, Mathf.Clamp01(queueCount / 10f));
        }

        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / duration);

            Vector3 curvedPos =
                Mathf.Pow(1 - t, 2) * startPos +
                2 * (1 - t) * t * arcPeak +
                Mathf.Pow(t, 2) * endPos;
            // Starts next swap animation if the last one is 0.1% complete
            target.visual.position = curvedPos;
            if (t >= 0.001f && !nextSwapStarted)
            {
                nextSwapStarted = true;
                TryProcessNextSwap();  // Start next swap early
            }

            yield return null;
        }

        // Finalize swap logic
        SwapGridPositions(dragged, target);

        // Reset visual position
        target.visual.localPosition = Vector3.zero;

        dragged.isSwapping = false;
        target.isSwapping = false;
        isSwapping = false;

        // Process next swap if queued
        TryProcessNextSwap();
    }

    private void SwapGridPositions(Rune dragged, Rune target)
    {
        // Swap in grid array
        runes[dragged.x, dragged.y] = target;
        runes[target.x, target.y] = dragged;

        // Swap coordinates
        int tempX = dragged.x, tempY = dragged.y;
        dragged.x = target.x; dragged.y = target.y;
        target.x = tempX; target.y = tempY;

        // Swap GameObject positions
        Vector3 draggedLocalPos = dragged.transform.localPosition;
        Vector3 targetLocalPos = target.transform.localPosition;

        dragged.transform.localPosition = targetLocalPos;
        target.transform.localPosition = draggedLocalPos;

        // Rename runes (for debugging)
        dragged.name = dragged.type + $"Rune (x{dragged.x}, y{dragged.y})";
        target.name = target.type + $"Rune (x{target.x}, y{target.y})";
    }


    public void SwapRunes(Rune dragged, Rune target) // Old swap system, eliminate it if new swap system gets aproved
    {
        // Allow immediate repeated swaps; no isSwapping lock or cooldowns required
        if (dragged == null || dragged.isSwapping || target == null || target.isSwapping || dragged.visual == null || target.visual == null)
            return;

        dragged.isSwapping = true;
        target.isSwapping = true;

        // Kill and reset any ongoing animations on the target visual
        target.visual.DOKill();
        target.visual.localScale = Vector3.one;
        target.visual.localPosition = Vector3.zero;

        // Play sound
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), runeSwitchClip);

        // Optional punch/bounce for visual feedback
        target.visual.DOPunchScale(Vector3.one * 0.4f, 0.2f, 10, 1f).SetEase(Ease.OutQuad);

        // Arc setup (target visual animates toward dragged’s position)
        Vector3 startPos = target.visual.position;
        Vector3 endPos = dragged.transform.position;

        Vector3 mid = (startPos + endPos) * 0.5f;
        Vector3 direction = (endPos - startPos).normalized;
        Vector3 perpendicular = Vector3.Cross(direction, Vector3.forward);
        float arcHeight = 0.7f;
        Vector3 arcPeak = mid + perpendicular * arcHeight;

        float t = 0f;
        float duration = 0.1f;

        // Animate target.visual along a curved arc toward dragged’s transform
        DOTween.To(() => t, val =>
        {
            t = val;
            Vector3 curvedPos =
                Mathf.Pow(1 - t, 2) * startPos +
                2 * (1 - t) * t * arcPeak +
                Mathf.Pow(t, 2) * endPos;

            target.visual.position = curvedPos;
        }, 1f, duration).SetEase(Ease.InOutQuad).OnComplete(() =>
        {
            // After animation completes, perform the logical and GameObject swap

            // Swap runes in grid
            runes[dragged.x, dragged.y] = target;
            runes[target.x, target.y] = dragged;

            // Swap their logical coordinates
            int tempX = dragged.x, tempY = dragged.y;
            dragged.x = target.x; dragged.y = target.y;
            target.x = tempX; target.y = tempY;

            // Swap their actual GameObject positions (not visuals)
            Vector3 draggedLocalPos = dragged.transform.localPosition;
            Vector3 targetLocalPos = target.transform.localPosition;

            dragged.transform.localPosition = targetLocalPos;
            target.transform.localPosition = draggedLocalPos;

            // Reset target visual back to center of new parent
            target.visual.localPosition = Vector3.zero;

            // Keep dragged visual under cursor
            if (dragged.isDragging)
            {
                Vector2 cursPos = Input.touchCount > 0
                    ? (Vector2)Camera.main.ScreenToWorldPoint(Input.GetTouch(0).position)
                    : (Vector2)Camera.main.ScreenToWorldPoint(Input.mousePosition);

                dragged.visual.position = cursPos;
            }
            else
            {
                dragged.visual.localPosition = Vector3.zero;
            }

            // Rename for debugging
            dragged.name = dragged.type + $"Rune (x{dragged.x}, y{dragged.y})";
            target.name = target.type + $"Rune (x{target.x}, y{target.y})";

            dragged.isSwapping = false;
            target.isSwapping = false;
        });
    }

    #endregion

    private int RuneMatching()
    {
        configsHandler.canCountdown = false;

        int matchCount = 0;
        matchedRunes.Clear(); // Reuse cached HashSet

        // Horizontal matches
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width - 2; x++)
            {
                Rune rune1 = runes[x, y];
                Rune rune2 = runes[x + 1, y];
                Rune rune3 = runes[x + 2, y];

                if (rune1 != null && rune2 != null && rune3 != null &&
                    rune1.type == rune2.type && rune1.type == rune3.type)
                {
                    matchedRunes.Add(rune1);
                    matchedRunes.Add(rune2);
                    matchedRunes.Add(rune3);
                    matchCount++;
                }
            }
        }

        // Vertical matches
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height - 2; y++)
            {
                Rune rune1 = runes[x, y];
                Rune rune2 = runes[x, y + 1];
                Rune rune3 = runes[x, y + 2];

                if (rune1 != null && rune2 != null && rune3 != null &&
                    rune1.type == rune2.type && rune1.type == rune3.type)
                {
                    matchedRunes.Add(rune1);
                    matchedRunes.Add(rune2);
                    matchedRunes.Add(rune3);
                    matchCount++;
                }
            }
        }

        // Group runes by type
        Dictionary<Types, int> typeGroups = new Dictionary<Types, int>();
        foreach (Rune rune in matchedRunes)
        {
            if (rune != null)
            {
                if (typeGroups.ContainsKey(rune.type))
                    typeGroups[rune.type]++;
                else
                    typeGroups[rune.type] = 1;
            }
        }

        isAttacking = true;
        // Process match groups and apply damage
        foreach (var kvp in typeGroups)
        {
            Types type = kvp.Key;
            int count = kvp.Value;

            //Debug.Log($"Matched {count} runes of type {type}");
            //Debug.Log("MatchCount: " + matchCount);
            // To use later, to change the weight of a match. Ex:. If 6 runes match maybe it counts as 2 attacks.

            configsHandler.RunesDamage(type);

            if (configsHandler.IsThereCharacters() &&
                !(configsHandler.physicalAttack &&
                !configsHandler.absorbedAttack &&
                !configsHandler.repelledAttack &&
                !configsHandler.blockedAttack))
            {
                configsHandler.ppLeft -= configsHandler.ReductionPerCombo;
            }


            if (count > 3) matchCount = 1;
        }
        isAttacking = false;

        if (matchCount > 0)
        {
            attacks += matchCount;
            combo = attacks - 1;
        }

        // Destroy matched runes using cached audio source
        foreach (Rune r in matchedRunes)
        {
            if (r != null)
            {
                // Use cached audio source and pre-loaded clip
                ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), runeClearClip);

                DOTween.Kill(r.visual); // Stop animation
                r.visual.DOScale(Vector3.zero, 0.10f).SetEase(Ease.InBack).OnComplete(() =>
                {
                    //Destroy(r.gameObject);
                    runePool.ReturnRune(r.gameObject); // Return to pool
                });
                runes[r.x, r.y] = null;
            }
        }

        return matchCount;
    }

    private IEnumerator CollapseRunes()
    {
        collapseEnded = false;
        for (int x = 0; x < width; x++)
        {
            for (int y = height - 1; y >= 0; y--) // Start from bottom row
            {
                if (runes[x, y] == null)
                {
                    // Look for a rune above (toward smaller y)
                    for (int ny = y - 1; ny >= 0; ny--)
                    {
                        if (runes[x, ny] != null)
                        {
                            Rune fallingRune = runes[x, ny];


                            if (fallingRune != null && fallingRune.gameObject != null)
                            {

                                runes[x, y] = fallingRune;
                                runes[x, ny] = null;

                                // Update coordinates
                                fallingRune.y = y;

                                // Kill any running tweens to prevent overlap
                                DOTween.Kill(fallingRune.transform);

                                // Animate down to the new position
                                int flatIndex = y * width + x;
                                Vector3 newPos = cachedLocalPositions[flatIndex];
                                fallingRune.transform.DOLocalMove(newPos, 0.20f).SetEase(Ease.InOutQuad);

                                // Rename for debugging
                                fallingRune.name = fallingRune.type + $"Rune (x{fallingRune.x}, y{fallingRune.y})";
                                break;
                            }
                        }
                    }
                }
            }
        }
        collapseEnded = true;

        yield return DOTween.Sequence().AppendInterval(0.20f).WaitForCompletion(); // Stops couroutine for 0.25 seconds
    }

    private IEnumerator SpawnNewRunes()
    {
        bool shouldForceMatch = false;

        // Resets combo if player miss attack
        if (enemy != null && enemy.missCheck)
        {
            combo = 0;
        }
        else
        {
            // Control combo chances, if roll is smaller of chances of a combo array it forces a match to cause the combo
            if (!configsHandler.infiniteToggle.GetComponent<Toggle>().isOn)
            {
                if (combo >= 0 && combo < configsHandler.ComboChance.Length)
                {
                    int roll = UnityEngine.Random.Range(0, 100);

                    if (roll < configsHandler.ComboChance[combo])
                    {
                        shouldForceMatch = true;
                        combo++;
                    }
                }
                else
                {
                    shouldForceMatch = false;
                }
            }
            else
            {
                shouldForceMatch = true;
                combo++;
            }
        }

        Types forcedType = (Types)UnityEngine.Random.Range(0, typesCount);

        // Reuse cached collections to reduce GC pressure
        emptySpotsList.Clear();
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                if (runes[x, y] == null)
                {
                    emptySpotsList.Add(new Vector2Int(x, y));
                }
            }
        }

        // Reuse cached HashSet for forced positions
        forcedPositionsSet.Clear();

        if (shouldForceMatch && emptySpotsList.Count >= 3)
        {
            // Try to find a group of 3+ adjacent positions (horizontal first, then vertical)
            bool matchPlaced = false;

            // Try horizontal
            for (int y = 0; y < height && !matchPlaced; y++)
            {
                for (int x = 0; x < width - 2 && !matchPlaced; x++)
                {
                    Vector2Int a = new Vector2Int(x, y);
                    Vector2Int b = new Vector2Int(x + 1, y);
                    Vector2Int c = new Vector2Int(x + 2, y);

                    if (emptySpotsList.Contains(a) && emptySpotsList.Contains(b) && emptySpotsList.Contains(c))
                    {
                        forcedPositionsSet.Add(a);
                        forcedPositionsSet.Add(b);
                        forcedPositionsSet.Add(c);
                        matchPlaced = true;
                    }
                }
            }

            // Try vertical if no horizontal match found
            for (int x = 0; x < width && !matchPlaced; x++)
            {
                for (int y = 0; y < height - 2 && !matchPlaced; y++)
                {
                    Vector2Int a = new Vector2Int(x, y);
                    Vector2Int b = new Vector2Int(x, y + 1);
                    Vector2Int c = new Vector2Int(x, y + 2);

                    if (emptySpotsList.Contains(a) && emptySpotsList.Contains(b) && emptySpotsList.Contains(c))
                    {
                        forcedPositionsSet.Add(a);
                        forcedPositionsSet.Add(b);
                        forcedPositionsSet.Add(c);
                        matchPlaced = true;
                    }
                }
            }

            // Randomly add a few more forced runes if there's space
            int maxForced = UnityEngine.Random.Range(3, emptySpotsList.Count + 1);
            foreach (var spot in emptySpotsList)
            {
                if (forcedPositionsSet.Count >= maxForced) break;
                if (!forcedPositionsSet.Contains(spot))
                    forcedPositionsSet.Add(spot);
            }
        }

        // Refill runes using cached collections
        foreach (Vector2Int pos in emptySpotsList)
        {
            //GameObject runeObj = Instantiate(runePrefab, transform);
            GameObject runeObj = runePool.GetRune();
            Types type = (cachedIsThereCharacters && shouldForceMatch && forcedPositionsSet.Contains(pos))
                ? forcedType
                : (Types)UnityEngine.Random.Range(0, typesCount);

            // Safely set the rune sprite with null checks
            runeObj.GetComponent<Image>().sprite = runeSprites[type];
            runeObj.transform.GetChild(0).GetChild(0).GetComponent<Image>().sprite = runeSprites[type];
            Rune rune = runeObj.GetComponent<Rune>();
            rune.Initialize(pos.x, pos.y, this);
            rune.type = type;

            // Use StringBuilder for efficient string concatenation
            stringBuilder.Clear();
            stringBuilder.Append(type.ToString());
            stringBuilder.Append("Rune (x");
            stringBuilder.Append(pos.x);
            stringBuilder.Append(", y");
            stringBuilder.Append(pos.y);
            stringBuilder.Append(")");
            rune.name = stringBuilder.ToString();

            runes[pos.x, pos.y] = rune;

            int flatIndex = pos.y * width + pos.x;
            Vector3 targetPos = cachedLocalPositions[flatIndex];
            rune.transform.localPosition = targetPos + Vector3.up * 200f;
            rune.transform.DOLocalMove(targetPos, 0.20f).SetEase(Ease.OutBack);
        }
        yield return DOTween.Sequence().AppendInterval(0.20f).WaitForCompletion();
    }

    public IEnumerator DestroyRunes() // Destroy runes animation after all players die
    {
        if (!configsHandler.AreAllActivePlayersDead()) // Only proceeds with the method when if all players are dead
            yield break;

        List<Sequence> activeSequences = new();
        float maxFallTime = 0f;


        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Rune rune = runes[x, y];
                if (rune != null && rune.visual != null)
                {
                    rune.runeImage.color = new Color(1f, 1f, 1f, 0.0f); // Transparent

                    Transform visual = rune.visual.transform;
                    DOTween.Kill(visual);

                    int capturedX = x; // Capture x reference of rune
                    int capturedY = y; // // Capture y reference of rune

                    float delay = UnityEngine.Random.Range(0f, 0.15f); // Delay for when the animation starts
                    float fallDuration = UnityEngine.Random.Range(0.7f, 1.7f); // Overall falling speed
                    float rotateAmount = UnityEngine.Random.Range(-120f, 120f); // More spin
                    float xDrift = UnityEngine.Random.Range(-0.4f, 0.4f); // Slight sideways motion

                    // Track longest animation for yield timing
                    float totalTime = delay + fallDuration;
                    if (totalTime > maxFallTime)
                        maxFallTime = totalTime;

                    Sequence destroySequence = DOTween.Sequence(); // Delay for runes to fall at different times
                    destroySequence.PrependInterval(delay);

                    destroySequence.Join(
                        visual.DOLocalMoveY(visual.localPosition.y - 1000f, fallDuration) // Falling Animation
                            .SetEase(Ease.InQuad)
                    );

                    destroySequence.Join(
                        visual.DOLocalMoveX(visual.localPosition.x + xDrift, fallDuration) // Drift in x axis
                            .SetEase(Ease.OutQuad)
                    );

                    destroySequence.Join(
                        visual.DOLocalRotate(new Vector3(0, 0, rotateAmount), fallDuration, RotateMode.FastBeyond360) // Rotation animation
                            .SetEase(Ease.OutCubic)
                    );

                    destroySequence.OnComplete(() =>  // After falling deactivate runes
                    {
                        if (runes[capturedX, capturedY] != null)
                            runes[capturedX, capturedY].gameObject.SetActive(false);
                    });

                    activeSequences.Add(destroySequence);
                }
            }
        }

        yield return new WaitForSeconds(maxFallTime);
        configsHandler.FailPopup.SetActive(true); // Activates Fail popup
    }

    public void ReviveRunes() // makes runes go back in place after destroy animation if player revives
    {
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
            Rune rune = runes[x, y];
            if (rune != null && rune.visual != null)
                {
                    rune.gameObject.SetActive(true); // Reactivate the runes

                    // Reset position
                    rune.visual.localPosition = Vector3.zero;

                    // Reset rotation
                    rune.visual.localRotation = Quaternion.identity;

                    // Reset alpha
                    if (rune.runeImage != null)
                        rune.runeImage.color = new Color(1f, 1f, 1f, 0.33f);
                }
            }
        }

    }

    public void HandleMatchesAfterMove() // Main routine after player drag
    {
        StartCoroutine(HandleMatchRoutine());
    }

    private IEnumerator HandleMatchRoutine()
    {
        isBoardBusy = true;

        int totalMatches = 0;

        do
        {
            yield return new WaitForSeconds(0.10f); // Let animations settle

            int matches = RuneMatching(); // Count matches in this pass

            if (matches > 0)
            {
                totalMatches += matches;

                yield return new WaitForSeconds(0.05f); // Wait for destruction
                yield return StartCoroutine(CollapseRunes());
                yield return new WaitForSeconds(0.05f); // Wait for collapse
                yield return StartCoroutine(SpawnNewRunes());
                //yield return new WaitForSeconds(0.15f);
            }
            else
            {
                break;
            }

        } while (true);

        yield return new WaitForSeconds(0.20f);
        isBoardBusy = false;

        yield return new WaitForSeconds(0.1f);

        yield return StartCoroutine(HandleStunAndCoins());

        if (configsHandler.AreAllEnemiesDead()) configsHandler.win = true;


        yield return StartCoroutine(SwitchTurns()); //Check if it switches turns after the grid is settled
    }

    public IEnumerator SwitchTurns()
    {
        configsHandler.canCountdown = false;

        // Use cached values to reduce expensive method calls
        if (cachedIsThereCharacters && cachedIsPPLeft)
        {
            if (configsHandler.playerTurn)
            {
                if (configsHandler.parryCount > 0 && !enemy.isStunned) // if enemy parried executes a counter
                {
                    StartCoroutine(configsHandler.EnemyCounter(configsHandler.parryCount));
                }

                configsHandler.playerActions--;          // Represents player actions, starts with 1 but can gain 1 more for each crit, weak or super effective attack. Max is 4 actions
                configsHandler.playerEmptyActions++;    // Represents number of used actions

                aiConfs.AddAction(configsHandler.playerEmptyActions, attacks); // Counts the number of combos for each action

                // checks if the player did crit, if not, resets the cooldown
                PlayerInterface player = configsHandler.playersInterface[configsHandler.GetSelectedPlayer()].GetComponent<PlayerInterface>();

                if (!configsHandler.CantPlayerPlay() && cachedIsThereAnyPlayerNotInCooldown)
                    if (player.didCrit) player.didCrit = false;
                    else if (player.AttackCooldown.fillAmount <= 0.0001f && !configsHandler.isCounter) player.AttackCooldown.fillAmount = 1; //Removes player cooldown if he is about to receive a counter (right now players on cooldown dont get attacked)

                if (configsHandler.playerEmptyActions >= 4 || configsHandler.playerActions <= 0 && !configsHandler.isCounter) // player got no actions switch turns
                {
                    aiConfs.EndTurn(); // Stops counting the turn combos
                    didTurnsSwitch = true;
                    configsHandler.playerTurn = false;
                    ResetActions();
                    configsHandler.playerTurns++;
                    configsHandler.turns++;
                    yield return StartCoroutine(SwitchTurns());
                    yield break;
                }
            }
            else  // enemy turn
            {
                combo = 0;
                while (isBoardBusy) yield return null;
                if (configsHandler.enemyFirstStrike)
                {
                    GameObject.Find("FirstAdvantage").GetComponent<FirstAdvantageAnim>().PlayCenterAnimation(); // only shows if enemy did first strike
                }
                else
                {
                    GameObject.Find("SwitchTurnsEffect").GetComponent<SwitchTurnsAnimation>().SwitchTurns("Enemy Turn", Color.red); // starts the switch turns animation
                }

                yield return StartCoroutine(configsHandler.EnemyAttack());
                while (configsHandler.isCounter) yield return null;
                yield return new WaitForSeconds(1.5f);
                configsHandler.enemyTurns++;
                configsHandler.turns++;
                ResetActions();
                didTurnsSwitch = true;
                configsHandler.playerTurn = true;

                // Update cache after turn switch
                UpdateMethodCallCache();

                if (!configsHandler.CantPlayerPlay() && cachedIsThereAnyPlayerNotInCooldown) GameObject.Find("SwitchTurnsEffect").GetComponent<SwitchTurnsAnimation>().SwitchTurns("Player Turn", Color.cyan); // starts the switch turns animation
                aiConfs.StartNewTurn(configsHandler.playerTurns); // Starts counting the turn combos
                if (configsHandler.CantPlayerPlay() || !cachedIsThereAnyPlayerNotInCooldown) StartCoroutine(SwitchTurns());
                yield break;
            }
        }
        yield return null;
    }

    public void ResetActions() // reset all actions for both enemy and players
    {
        configsHandler.playerActions = 1;
        configsHandler.playerEmptyActions = 0;
        configsHandler.enemyActions = 1;
        configsHandler.enemyEmptyActions = 0;
    }

    private IEnumerator HandleStunAndCoins()
    {
        if (enemy != null)
        {

            switch (enemy.stunPhase)
            {
                case StunPhase.Normal: // If Stuns, gives coins in next action
                    if (combo > 4 && !(configsHandler.parryCount > 0)) // if enemy perform a parry it doesnt get stunned
                    {
                        enemy.isStunned = true;
                        enemy.canReceiveCoins = true;
                        enemy.stunPhase = StunPhase.Stunned;
                    }
                    break;

                case StunPhase.Stunned: // After receiving attacks gives coins and stun is removed
                    enemy.isStunned = false;
                    enemy.canReceiveCoins = false;
                    enemy.stunPhase = StunPhase.Normal;
                    break;

                default:
                    break;
            }
        }
        yield return null;
    }
}
