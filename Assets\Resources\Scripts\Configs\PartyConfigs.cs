using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Linq;
using DG.Tweening;

/// <summary>
/// Class that handles the party configs
/// </summary>
public class PartyConfigs : MonoBehaviour
{
    // ConfigsHandler reference
    ConfigsHandler configsHandler;
    public StickyOverlayForStock stickyOverlayForStock;
    public StickyUIElement stickyUIElement;

    // ToggleGroups of the party being used and the party that is being eddited
    ToggleGroup useParty, editParty;

    // Single party GameObject that will be dynamically updated
    public GameObject singlePartyOBJ;
    GameObject activePlayers;
    GameObject stockPlayers;

    GridLayoutGroup activeLayout, stockLayout;

    public GameObject cardPrefab;

    // Bool that checks if the game has started
    bool didGameStart = false;

    // Int that controls the index of the party
    public int partyIndex = 0;

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    void Start()
    {
        // Get the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // Get the ToggleGroup of the party being used
        useParty = GetComponent<ToggleGroup>();

        // Get the ToggleGroup of the party being eddited
        editParty = transform.parent.parent.GetChild(0).GetComponent<ToggleGroup>();

        // Add the onEndEdit listener to the TMP_InputField
        TMP_InputField partyName = singlePartyOBJ.transform.GetChild(0).GetComponent<TMP_InputField>();
        partyName.onEndEdit.AddListener(text => PartyNameEditted(text, partyName));

        activePlayers = singlePartyOBJ.transform.GetChild(1).GetChild(1).gameObject;
        stockPlayers = singlePartyOBJ.transform.GetChild(2).GetChild(1).gameObject;
        activeLayout = activePlayers.GetComponent<GridLayoutGroup>();
        stockLayout = stockPlayers.GetComponent<GridLayoutGroup>();

        SetupButtonListeners(singlePartyOBJ);

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));

        LoadAllTypeSprites();

        LoadPrefabs();

        //RefreshPartyUI();

        // Setup "select party" toggle listeners
        SetupSelectPartyToggleListeners();

        // Update the UI to each party
        Toggle[] editPartyToggles = editParty.GetComponentsInChildren<Toggle>();
        foreach (var toggle in editPartyToggles)
        {
            toggle.onValueChanged.AddListener((isOn) =>
            {
                // play the select sound effect
                ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

                if (isOn)
                {
                    // Delay slightly to ensure the ToggleGroup has updated
                    StartCoroutine(RefreshNextFrame());
                }
            });
        }
    }

    IEnumerator RefreshNextFrame() // Without this you would have to click the toggle twice
    {
        yield return null; // wait one frame
        RefreshPartyUI(); // This will now update the single GameObject with data from the selected party
        stickyUIElement.SwitchStickyOriginal(partyIndex);

        // Reset the "select party" toggle group when switching between different parties
        ResetSelectPartyToggles();
    }

    void SetupButtonListeners(GameObject party)
    {
        var allButtons = party.GetComponentsInChildren<Button>();
        foreach (var slot in allButtons)
        {

            // Clear existing listeners to avoid duplicates
            slot.onClick.RemoveAllListeners();

            if (slot.name != "remChar" && slot.name != "AstralPlanBtn") // if the button's name isn't "remChar" and "AstralPlanBtn" it gives the listener for it for selecting the slots of the party
            {
                slot.onClick.AddListener(delegate { StartCoroutine(SlotSelected(slot)); });
                var sbImage = slot.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                if (sbImage != null)
                {
                    sbImage.enabled = false;
                }
            }
        }
    }

    void SetupSelectPartyToggleListeners()
    {
        if (singlePartyOBJ == null) return;

        // Get the single "select party" toggle at child index 3
        Transform selectPartyTransform = singlePartyOBJ.transform.GetChild(3);
        Toggle selectPartyToggle = selectPartyTransform.GetComponent<Toggle>();

        if (selectPartyToggle == null) return;

        // Clear existing listeners to avoid duplicates
        selectPartyToggle.onValueChanged.RemoveAllListeners();

        selectPartyToggle.onValueChanged.AddListener((isOn) =>
        {
            // play the select sound effect
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

            if (isOn)
            {
                // Set the selected party to the currently displayed party (partyIndex)
                configsHandler.SetSelectedParty(partyIndex);
            }
            else
            {
                // If toggle is turned off, set selected party to -1 (no party selected)
                configsHandler.SetSelectedParty(-1);
            }
        });
    }

    void ResetSelectPartyToggles()
    {
        if (singlePartyOBJ == null) return;

        // Get the single "select party" toggle at child index 3
        Transform selectPartyTransform = singlePartyOBJ.transform.GetChild(3);
        Toggle selectPartyToggle = selectPartyTransform.GetComponent<Toggle>();

        if (selectPartyToggle == null) return;

        // Turn off the select party toggle
        selectPartyToggle.isOn = false;

        // Set selected party to -1 (no party selected)
        configsHandler.SetSelectedParty(-1);
    }

    void LoadPrefabs()
    {
        // Instantiates the 4 prefabs
        for (int i = 0; i < 4; i++)
        {
            Instantiate(cardPrefab, activePlayers.transform);
            activePlayers.transform.GetChild(i).GetComponent<playerCard>().Init(true, i, "AP" + i);
            Debug.Log("AP" + i + "IsActive: " + activePlayers.transform.GetChild(i).GetComponent<playerCard>().isInActive + " Character is Null: " + (activePlayers.transform.GetChild(i).GetComponent<playerCard>().character == null));
            if (activePlayers.transform.GetChild(i).GetComponent<playerCard>().character != null)
            {
                Debug.Log("AP" + i + "Name: " + activePlayers.transform.GetChild(i).GetComponent<playerCard>().character.name);
            }

        }

        for (int i = 0; i < 12; i++)
        {
            Instantiate(cardPrefab, stockPlayers.transform);
            stockPlayers.transform.GetChild(i).GetComponent<playerCard>().Init(false, i, "SP" + i);
            Debug.Log("SP" + i + "IsActive: " + stockPlayers.transform.GetChild(i).GetComponent<playerCard>().isInActive + " Character is Null: " + (stockPlayers.transform.GetChild(i).GetComponent<playerCard>().character == null));
            if (stockPlayers.transform.GetChild(i).GetComponent<playerCard>().character != null)
            {
                Debug.Log("SP" + i + "Name: " + stockPlayers.transform.GetChild(i).GetComponent<playerCard>().character.name);
            }
        }

        StartCoroutine(FinalizeGridLayout(activePlayers, activeLayout));
        StartCoroutine(FinalizeGridLayout(stockPlayers, stockLayout));
    }

        IEnumerator FinalizeGridLayout(GameObject parent, GridLayoutGroup gridLayoutGroup)
        {
            // Wait for layout to complete
            yield return new WaitForEndOfFrame();

            List<Vector3> cachedLocalPositions = new List<Vector3>();

            foreach (Transform child in parent.transform)
            {
                cachedLocalPositions.Add(child.localPosition);
            }

            // Disable GridLayoutGroup so we can animate freely
            gridLayoutGroup.enabled = false;

            // Reapply positions (to "bake in" the layout)
            int i = 0;
            foreach (Transform child in parent.transform)
            {
                child.localPosition = cachedLocalPositions[i];
                i++;
            }

            // Reset grid bounds calculation flag so it gets recalculated with new positions
            //gridBoundsCalculated = false;
        }

    void Update()
    {
        if (configsHandler.turns != 1) didGameStart = true; // checks if the game has started

        // if the game has started, disables the toggles to select the party
        if (didGameStart) foreach (var useToggle in useParty.gameObject.GetComponentsInChildren<Toggle>()) useToggle.interactable = false;

        // Update speed display for the single party GameObject
        if (singlePartyOBJ != null)
        {
            singlePartyOBJ.transform.GetChild(4).GetChild(1).GetComponent<TextMeshProUGUI>().text = configsHandler.CalculateAverageActivePartySpeed().ToString() == "NaN" ? "Select party" : configsHandler.CalculateAverageActivePartySpeed().ToString();
        }

        // gets the index of the party
        partyIndex = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();


    }

    public void SwapCharacters(playerCard draggedCard, playerCard targetCard)
    {
        if (draggedCard == null || targetCard == null) return;

        if (draggedCard.isInActive && targetCard.isInActive) // if both cards are in the same section (active)
        {
            // Swap characters in the party data
            BattleCharacter temp = configsHandler.GetPartyCharacters(partyIndex).activeCharacters[draggedCard.partyCharacterIndex];
            configsHandler.SetCharacterToParty(partyIndex, draggedCard.partyCharacterIndex, draggedCard.isInActive, configsHandler.GetPartyCharacters(partyIndex).activeCharacters[targetCard.partyCharacterIndex]);
            configsHandler.SetCharacterToParty(partyIndex, targetCard.partyCharacterIndex, targetCard.isInActive, temp);
        }

        if (!draggedCard.isInActive && !targetCard.isInActive) // if both cards are in the stock section
        {
            // Swap characters in the party data
            BattleCharacter temp = configsHandler.GetPartyCharacters(partyIndex).stockCharacters[draggedCard.partyCharacterIndex];
            configsHandler.SetCharacterToParty(partyIndex, draggedCard.partyCharacterIndex, draggedCard.isInActive, configsHandler.GetPartyCharacters(partyIndex).stockCharacters[targetCard.partyCharacterIndex]);
            configsHandler.SetCharacterToParty(partyIndex, targetCard.partyCharacterIndex, targetCard.isInActive, temp);
        }

        //Also swap the partyCharacterIndex
        int tempIndex = draggedCard.partyCharacterIndex;
        draggedCard.partyCharacterIndex = targetCard.partyCharacterIndex;
        targetCard.partyCharacterIndex = tempIndex;


        // Swap GameObject positions
        Vector3 draggedLocalPos = draggedCard.initialPosition;
        Vector3 targetLocalPos = targetCard.transform.localPosition;

        //draggedCard.transform.localPosition = targetLocalPos;
        //targetCard.transform.localPosition = draggedLocalPos;
        draggedCard.transform.DOLocalMove(targetLocalPos, 0.3f).SetEase(Ease.OutCubic);
        targetCard.transform.DOLocalMove(draggedLocalPos, 0.3f).SetEase(Ease.OutCubic);

    }


    public void RefreshPartyUI(int onlyPartyIndex = -1)
    {
        // Check if essential components are available
        if (singlePartyOBJ == null || configsHandler == null) return;

        // Use the current partyIndex to get the data for the single party GameObject
        int targetPartyIndex = onlyPartyIndex != -1 ? onlyPartyIndex : partyIndex;

        // gets the TMP_InputField for the name of the party and the PartyCharacters
        TMP_InputField partyName = singlePartyOBJ.transform.GetChild(0).GetComponent<TMP_InputField>();
        PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(targetPartyIndex);

        // if the PartyCharacters is null, return early
        if (partyCharacters == null) return;

        // if the TMP_InputField isn't focused, updates the name
        if (!partyName.isFocused) partyName.text = partyCharacters.name;

        // updates the active characters being displayed
        foreach (Button activeButtons in singlePartyOBJ.transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar"))
        {
            // gets the character
            BattleCharacter character = partyCharacters.activeCharacters[activeButtons.transform.GetSiblingIndex()];
            if (character != null) // if the character isn't null, it show the values
            {
                activeButtons.transform.GetChild(0).gameObject.SetActive(false); //EmptySign
                activeButtons.transform.GetChild(1).gameObject.SetActive(true); //Player Sprite
                activeButtons.transform.GetChild(2).gameObject.SetActive(true); //Rarity
                activeButtons.transform.GetChild(3).gameObject.SetActive(true); //Overlay
                activeButtons.transform.GetChild(4).gameObject.SetActive(true); //Lv
                activeButtons.transform.GetChild(5).gameObject.SetActive(true); //Player Name
                activeButtons.transform.GetChild(6).gameObject.SetActive(true); //BigElemDef
                activeButtons.transform.GetChild(7).gameObject.SetActive(true); //SmallElemDef
                activeButtons.transform.GetChild(8).gameObject.SetActive(true); //BigElemAtk
                activeButtons.transform.GetChild(9).gameObject.SetActive(true); //Stars Container
                activeButtons.transform.GetChild(11).gameObject.SetActive(true); //Status
                activeButtons.transform.GetChild(12).gameObject.SetActive(true); //Remove Button
                var remCharButtonGO = activeButtons.transform.GetChild(12).gameObject;
                remCharButtonGO.SetActive(true);

                Button remCharButton = remCharButtonGO.GetComponent<Button>();
                if (remCharButton != null)
                {
                    remCharButton.onClick.RemoveAllListeners();  // clear old listeners
                    remCharButton.onClick.AddListener(delegate
                    {
                        // play the select sound effect
                        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
                        RemoveCharacterFromParty(remCharButton);
                    });
                }
                activeButtons.transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
                activeButtons.transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
                activeButtons.transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
                //activeButtons.transform.GetChild(4).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                //activeButtons.transform.GetChild(4).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                Types type1 = character.skills.GetHighestSpDef();
                activeButtons.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                Types type2 = character.skills.GetHighestSpAtk();
                activeButtons.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                Types type3 = character.skills.GetLowestSpDef();
                activeButtons.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                if (character.IsDead)
                {
                    activeButtons.transform.GetChild(11).gameObject.SetActive(true);
                    activeButtons.transform.GetChild(11).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    activeButtons.transform.GetChild(11).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";

                }
                else
                {
                    activeButtons.transform.GetChild(11).gameObject.SetActive(false);
                }

            }
            else // otherwise it hide the values
            {
                activeButtons.interactable = true;
                activeButtons.transform.GetChild(0).gameObject.SetActive(true);
                activeButtons.transform.GetChild(1).gameObject.SetActive(false);
                activeButtons.transform.GetChild(2).gameObject.SetActive(false);
                activeButtons.transform.GetChild(3).gameObject.SetActive(false);
                activeButtons.transform.GetChild(4).gameObject.SetActive(false);
                activeButtons.transform.GetChild(5).gameObject.SetActive(false);
                activeButtons.transform.GetChild(6).gameObject.SetActive(false);
                activeButtons.transform.GetChild(7).gameObject.SetActive(false);
                activeButtons.transform.GetChild(8).gameObject.SetActive(false);
                activeButtons.transform.GetChild(9).gameObject.SetActive(false);
                activeButtons.transform.GetChild(11).gameObject.SetActive(false);
                activeButtons.transform.GetChild(12).gameObject.SetActive(false);
            }
        }

        // updates the stock characters being displayed
        foreach (Button stockButtons in singlePartyOBJ.transform.GetChild(2).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar"))
        {
            // gets the character
            BattleCharacter character = partyCharacters.stockCharacters[stockButtons.transform.GetSiblingIndex()];

            if (character != null) // if the character isn't null, it show the values
            {
                bool isCharNotActive = partyCharacters.activeCharacters.FirstOrDefault(c => c == character) == null;
                bool isCharDead = character.IsDead;

                stockButtons.interactable = isCharNotActive;

                stockButtons.transform.GetChild(0).gameObject.SetActive(false); //EmptySign
                stockButtons.transform.GetChild(1).gameObject.SetActive(true); //Player Sprite
                stockButtons.transform.GetChild(2).gameObject.SetActive(true); //Rarity
                stockButtons.transform.GetChild(3).gameObject.SetActive(true); //Overlay
                stockButtons.transform.GetChild(4).gameObject.SetActive(true); //Lv
                stockButtons.transform.GetChild(5).gameObject.SetActive(true); //Player Name
                stockButtons.transform.GetChild(6).gameObject.SetActive(true); //BigElemDef
                stockButtons.transform.GetChild(7).gameObject.SetActive(true); //SmallElemDef
                stockButtons.transform.GetChild(8).gameObject.SetActive(true); //BigElemAtk
                stockButtons.transform.GetChild(9).gameObject.SetActive(true); //Stars Container
                stockButtons.transform.GetChild(11).gameObject.SetActive(!isCharNotActive); //Status
                stockButtons.transform.GetChild(12).gameObject.SetActive(isCharNotActive); //Remove Button
                var remCharButtonGO = stockButtons.transform.GetChild(12).gameObject;
                remCharButtonGO.SetActive(isCharNotActive);

                if (isCharNotActive) // only add listener if active
                {
                    Button remCharButton = remCharButtonGO.GetComponent<Button>();
                    if (remCharButton != null)
                    {
                        remCharButton.onClick.RemoveAllListeners();
                        remCharButton.onClick.AddListener(delegate
                        {
                            // play the select sound effect
                            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
                            RemoveCharacterFromParty(remCharButton);
                        });
                    }
                }
                else
                {
                    //Remove listeners if inactive
                    Button remCharButton = remCharButtonGO.GetComponent<Button>();
                    if (remCharButton != null)
                    {
                        remCharButton.onClick.RemoveAllListeners();
                    }
                }

                stockButtons.transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
                stockButtons.transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
                stockButtons.transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
                //stockButtons.transform.GetChild(5).GetChild(0).GetComponent<Image>().fillAmount = (character.maxHP == 0) ? 1 : (float)character.hP / character.maxHP;
                //stockButtons.transform.GetChild(5).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.hP.ToString();
                Types type1 = character.skills.GetHighestSpDef();
                stockButtons.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
                Types type2 = character.skills.GetHighestSpAtk();
                stockButtons.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
                Types type3 = character.skills.GetLowestSpDef();
                stockButtons.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

                if (isCharDead)
                {
                    stockButtons.transform.GetChild(11).gameObject.SetActive(true);
                    stockButtons.transform.GetChild(11).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    stockButtons.transform.GetChild(11).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";
                }
                else
                {
                    stockButtons.transform.GetChild(11).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                    stockButtons.transform.GetChild(11).GetChild(0).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                }
            }
            else // otherwise it hide the values
            {
                stockButtons.interactable = true;
                stockButtons.transform.GetChild(0).gameObject.SetActive(true);
                stockButtons.transform.GetChild(1).gameObject.SetActive(false);
                stockButtons.transform.GetChild(2).gameObject.SetActive(false);
                stockButtons.transform.GetChild(3).gameObject.SetActive(false);
                stockButtons.transform.GetChild(4).gameObject.SetActive(false);
                stockButtons.transform.GetChild(5).gameObject.SetActive(false);
                stockButtons.transform.GetChild(6).gameObject.SetActive(false);
                stockButtons.transform.GetChild(7).gameObject.SetActive(false);
                stockButtons.transform.GetChild(8).gameObject.SetActive(false);
                stockButtons.transform.GetChild(9).gameObject.SetActive(false);
                stockButtons.transform.GetChild(11).gameObject.SetActive(false);
                stockButtons.transform.GetChild(12).gameObject.SetActive(false);
            }
        }

        stickyOverlayForStock.RefreshStickyPartyUI();
    }

    void PartyNameEditted(string name, TMP_InputField text) // updates the party name
    {
        configsHandler.SetPartyName(partyIndex, name);
        RefreshPartyUI(partyIndex);
    }

    public IEnumerator SlotSelected(Button button) // makes the button slot selected
    {
        yield return null;

        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        HighlightStickyButton(button); // also selects the button on sticky overlay

        // Check if this is a stock button click and we have an active slot selected
        bool isStockButton = IsStockButton(button);
        bool isActiveButton = IsActiveButton(button);

        if (isStockButton && HasSelectedActiveSlot())
        {
            PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
            int stockIndex = button.transform.GetSiblingIndex();

            if (partyCharacters.stockCharacters[stockIndex] != null)
            {
                // Move character only if not null
                MoveStockCharacterToActiveSlot(button);
                yield break;
            }
            else
            {
                // Stock character null, just select the stock button
                foreach (var item in transform.GetComponentsInChildren<Button>().Where(b => b.name != "remChar")) // disables all the other selected buttons overlay
                {
                    var sb1Image = item.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                    if (sb1Image != null)
                    {
                        sb1Image.enabled = false;
                    }
                }

                var sbImage = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
                if (sbImage != null) sbImage.enabled = true;
                yield break;
            }
        }
        foreach (var item in transform.GetComponentsInChildren<Button>().Where(b => b.name != "remChar")) // disables all the other selected buttons overlay
        {
            var sbImage = item.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null)
            {
                sbImage.enabled = false;
            }
        }

        // enables the selected button overlay
        var selectedSbImage = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
        //if (selectedSbImage != null)
        //{
            selectedSbImage.enabled = true;
        //}
    }

    bool IsStockButton(Button button)
    {
        // Check if the button is in the stock section (child 2)
        return button.transform.IsChildOf(singlePartyOBJ.transform.GetChild(2));
    }

    bool IsActiveButton(Button button)
    {
        // Check if the button is in the active section (child 1)
        return button.transform.IsChildOf(singlePartyOBJ.transform.GetChild(1));
    }

    bool HasSelectedActiveSlot()
    {
        // Check if there's a selected active slot that is null (empty)
        var activeButtons = singlePartyOBJ.transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar");

        foreach (var activeButton in activeButtons)
        {
            var sbImage = activeButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null && sbImage.enabled)
            {
                // Check if this active slot has a null character
                PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
                int slotIndex = activeButton.transform.GetSiblingIndex();
                return partyCharacters.activeCharacters[slotIndex] == null;
            }
        }
        return false;
    }

    void MoveStockCharacterToActiveSlot(Button stockButton)
    {
        // Find the selected active slot
        var activeButtons = singlePartyOBJ.transform.GetChild(1).GetChild(1).GetComponentsInChildren<Button>().Where(b => b.name != "remChar");

        foreach (var activeButton in activeButtons)
        {
            var sbImage = activeButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sbImage != null && sbImage.enabled)
            {
                // Get the character from stock
                PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
                int stockSlotIndex = stockButton.transform.GetSiblingIndex();
                int activeSlotIndex = activeButton.transform.GetSiblingIndex();

                BattleCharacter stockCharacter = partyCharacters.stockCharacters[stockSlotIndex];

                if (stockCharacter != null)
                {
                    // Check if character is already in active party and remove it first
                    for (int i = 0; i < partyCharacters.activeCharacters.Length; i++)
                    {
                        if (partyCharacters.activeCharacters[i] == stockCharacter)
                        {
                            configsHandler.SetCharacterToParty(partyIndex, i, true, null);
                            break;
                        }
                    }

                    // Move character from stock to active
                    configsHandler.SetCharacterToParty(partyIndex, activeSlotIndex, true, stockCharacter);

                    // Clear the selection
                    sbImage.enabled = false;
                }
                break;
            }
        }
        RefreshPartyUI(partyIndex);

    }

    public void RemoveCharacterFromParty(Button button) // removes the character from the party
    {
        int slotIndex = button.transform.parent.GetSiblingIndex(); // gets the slot index
        bool isActive = button.transform.parent.name.StartsWith("AP"); // gets if the character is active or not

        configsHandler.SetCharacterToParty(partyIndex, slotIndex, isActive, null);
        //RefreshPartyUI(partyIndex);
    }

    public void HighlightStickyButton(Button clickedStockButton)
    {
        // Get index among non-"remChar" buttons
        Transform stockParent = clickedStockButton.transform.parent;
        var buttons = stockParent.GetComponentsInChildren<Button>().Where(b => b.name != "remChar").ToList();
        int index = buttons.IndexOf(clickedStockButton);
        if (index < 0) return;

        // Reference to the StickyOverlayForStock GameObject (assign this if not accessible globally)
        //GameObject stickyOverlay = /* assign your StickyOverlayForStock reference */;

        // Get corresponding sticky button
        var stickyButtons = stickyOverlayForStock.GetComponentsInChildren<Button>().Where(b => b.name != "remChar").ToList();
        if (index >= stickyButtons.Count) return;

        // Turn off all sbImage overlays first
        foreach (var button in stickyButtons)
        {
            var sb1Image = button.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
            if (sb1Image != null)
                sb1Image.enabled = false;
        }

        // Enable the sbImage of the matching sticky button
        var matchingStickyButton = stickyButtons[index];
        var sbImage = matchingStickyButton.GetComponentsInChildren<Image>().FirstOrDefault(it => it.name == "SB");
        if (sbImage != null)
            sbImage.enabled = true;
    }

    void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }

    Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }

}
