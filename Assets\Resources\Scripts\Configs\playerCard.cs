using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class playerCard : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IBeginDrag<PERSON><PERSON><PERSON>, IEndDragHandler
{
    GameObject selectedOverlay;
    GameObject card;
    ConfigsHandler configsHandler;
    PartyConfigs partyConfigs;

    Camera canvasCamera;

    PartyCharacters partyCharacters;
    public BattleCharacter character;

    Button cardButton;
    Button detailsButton;
    Button removeButton;
    Button addButton;

    public Vector2 initialPosition;

    public bool isInActive;

    public bool charInStockButAlsoInActive;

    public int partyCharacterIndex;

    private playerCard currentTarget = null;

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();


    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        canvasCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));

        LoadAllTypeSprites();

        selectedOverlay = transform.GetChild(0).gameObject;
        card = transform.GetChild(1).gameObject;

        cardButton = card.GetComponent<Button>();
        detailsButton = selectedOverlay.transform.GetChild(0).GetComponent<Button>();
        removeButton = selectedOverlay.transform.GetChild(1).GetComponent<Button>();
        addButton = selectedOverlay.transform.GetChild(2).GetComponent<Button>();

        cardButton.onClick.AddListener(SelectCard);
        detailsButton.onClick.AddListener(() => { if (!isInActive) RemoveFromParty(); else {} });
        removeButton.onClick.AddListener(RemoveFromParty);
        addButton.onClick.AddListener(AddToActiveParty);




    }

    public void Init(bool isActive, int index, string name)
    {
        isInActive = isActive;
        partyCharacterIndex = index;
        gameObject.name = name;

        if (configsHandler == null) configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        if (partyConfigs == null) partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();
        partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);
        if (isInActive) character = partyCharacters.activeCharacters[partyCharacterIndex];
        else character = partyCharacters.stockCharacters[partyCharacterIndex];
    }


    // Update is called once per frame
    void Update()
    {
        UpdateUI();

        if (!isInActive && !charInStockButAlsoInActive) // if the character is in the stock changes the details button to remove button
        {
            selectedOverlay.transform.GetChild(0).GetChild(0).GetComponent<TextMeshProUGUI>().text = "Remover";
            selectedOverlay.transform.GetChild(0).GetComponent<Image>().color = new Color(1f, 0f, 0f);

        }
    }

    void SelectCard()
    {

        foreach (var item in transform.parent.parent.parent.GetComponentsInChildren<playerCard>()) // disables all the other selected buttons overlay
        {
            if (item != this) item.selectedOverlay.SetActive(false);
        }

        // Don't allow selection if character is null
        if (character == null)
        return;

        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        if (selectedOverlay.activeSelf) selectedOverlay.SetActive(false);
        else selectedOverlay.SetActive(true);

        if (selectedOverlay.activeSelf)
        {
            if (isInActive || charInStockButAlsoInActive)
            {
                selectedOverlay.transform.GetChild(1).gameObject.SetActive(true);
                selectedOverlay.transform.GetChild(2).gameObject.SetActive(false);
            } else
            {
                selectedOverlay.transform.GetChild(1).gameObject.SetActive(false);
                selectedOverlay.transform.GetChild(2).gameObject.SetActive(true);
            }
        }

        gameObject.transform.SetAsLastSibling();

    }

    public void RemoveFromParty()
    {
        configsHandler.SetCharacterToParty(partyConfigs.partyIndex, partyCharacterIndex, isInActive, null);

        selectedOverlay.SetActive(false);
    }
    public void AddToActiveParty()
    {
        //Check if theres empty slots in the active party
        for (int i = 0; i < partyCharacters.activeCharacters.Length; i++)
        {
            if (partyCharacters.activeCharacters[i] == null)
            {
                configsHandler.SetCharacterToParty(partyConfigs.partyIndex, i, true, character);
                partyCharacterIndex = i;
                charInStockButAlsoInActive = true;

                // UpdateUI() will automatically refresh data and character reference

                break;
            }
        }

        selectedOverlay.SetActive(false);

    }

    void UpdateUI()
    {
        // 🔧 Always refresh party data to get the most current state
        partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);

        // 🔧 Refresh character reference from current party data
        if (isInActive) character = partyCharacters.activeCharacters[partyCharacterIndex];
        else character = partyCharacters.stockCharacters[partyCharacterIndex];

        // Variable to know if the character is in the stock but also in the active
        charInStockButAlsoInActive = !isInActive && partyCharacters.activeCharacters.FirstOrDefault(c => c == character) != null;

        if (character != null) // if the character isn't null, it show the values
        {
            card.transform.GetChild(0).gameObject.SetActive(false); //EmptySign
            card.transform.GetChild(1).gameObject.SetActive(true); //Player Sprite
            card.transform.GetChild(2).gameObject.SetActive(true); //Rarity
            card.transform.GetChild(3).gameObject.SetActive(true); //Overlay
            card.transform.GetChild(4).gameObject.SetActive(true); //Lv
            card.transform.GetChild(5).gameObject.SetActive(true); //Player Name
            card.transform.GetChild(6).gameObject.SetActive(true); //BigElemDef
            card.transform.GetChild(7).gameObject.SetActive(true); //SmallElemDef
            card.transform.GetChild(8).gameObject.SetActive(true); //BigElemAtk
            card.transform.GetChild(9).gameObject.SetActive(true); //Stars Container
            card.transform.GetChild(10).gameObject.SetActive(!charInStockButAlsoInActive); //Status

            card.transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
            card.transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            card.transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
            Types type1 = character.skills.GetHighestSpDef();
            card.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
            Types type2 = character.skills.GetHighestSpAtk();
            card.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
            Types type3 = character.skills.GetLowestSpDef();
            card.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

            if (character.IsDead)
            {
                card.transform.GetChild(10).gameObject.SetActive(true);
                card.transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";
            }
            else
            {
                card.transform.GetChild(10).gameObject.SetActive(false);
            }

            if (charInStockButAlsoInActive)
            {
                if (character.IsDead)
                {
                    card.transform.GetChild(10).gameObject.SetActive(true);
                    card.transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";
                }
                else
                {
                    card.transform.GetChild(10).gameObject.SetActive(true);
                    card.transform.GetChild(10).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                    card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                }
            }
        }
        else
        {
            card.transform.GetChild(0).gameObject.SetActive(true); //EmptySign
            card.transform.GetChild(1).gameObject.SetActive(false); //Player Sprite
            card.transform.GetChild(2).gameObject.SetActive(false); //Rarity
            card.transform.GetChild(3).gameObject.SetActive(false); //Overlay
            card.transform.GetChild(4).gameObject.SetActive(false); //Lv
            card.transform.GetChild(5).gameObject.SetActive(false); //Player Name
            card.transform.GetChild(6).gameObject.SetActive(false); //BigElemDef
            card.transform.GetChild(7).gameObject.SetActive(false); //SmallElemDef
            card.transform.GetChild(8).gameObject.SetActive(false); //BigElemAtk
            card.transform.GetChild(9).gameObject.SetActive(false); //Stars Container
            card.transform.GetChild(10).gameObject.SetActive(false); //Status
        }
    }

    void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }

    Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        initialPosition = transform.localPosition;

        // Disable raycast on the dragged object
        GetComponent<CanvasGroup>().blocksRaycasts = false;
    }

    public void OnDrag(PointerEventData eventData)
    {
        Vector2 cursorPos = canvasCamera.ScreenToWorldPoint(eventData.position);
        transform.position = cursorPos;

        transform.SetAsLastSibling();

        // Check what we're currently over during drag
        currentTarget = GetCardUnderPointer(eventData.position);

        // Optional: Visual feedback
        if (currentTarget != null && currentTarget != this)
        {
            // Highlight target card
            Debug.Log($"Currently over: {currentTarget.name}");
        }

    }

    public void OnEndDrag(PointerEventData eventData)
    {
        // Re-enable raycast
        CanvasGroup canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup != null)
            canvasGroup.blocksRaycasts = true;

        // Use the target we tracked during drag
        if (currentTarget != null && currentTarget != this)
        {
            partyConfigs.SwapCharacters(this, currentTarget);
        }
        else
        {
            // Return to original position
            transform.DOLocalMove(initialPosition, 0.3f).SetEase(Ease.OutCubic);
            Debug.Log("No valid target found for swap");
        }

        currentTarget = null;
    }

    private playerCard GetCardUnderPointer(Vector2 screenPosition)
    {
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = screenPosition
        };

        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);

        foreach (RaycastResult result in results)
        {
            if (result.gameObject == gameObject) continue; // Skip self

            playerCard card = result.gameObject.GetComponentInParent<playerCard>();
            if (card != null)
            {
                return card;
            }
        }

        return null;
    }

}
