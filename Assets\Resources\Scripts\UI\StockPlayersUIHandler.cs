using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class StockPlayersUIHandler : MonoBehaviour
{
    Vector3 origin; // The origin of the UI

    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    List<Button> stockChars = new(); // The stock characters

    Button cancelButton;

    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        origin = new(0f, -0.5671535f * transform.localScale.y, transform.position.z); // Set the origin

        cancelButton = transform.GetChild(0).GetComponent<Button>(); // Get the cancel button
        cancelButton.onClick.AddListener(() =>
        {
            gameObject.SetActive(false);
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
        }); // Add the listener to the button

        stockChars = transform.GetChild(2).GetComponentsInChildren<Button>().ToList(); // Get the stock characters

        // Add conditional listeners to the buttons based on character state
        foreach (var stockChar in stockChars)
        {
            int index = stockChar.transform.GetSiblingIndex();
            BattleCharacter stockCharacter = configsHandler.GetStockCharacter(index);

            // Clear any existing listeners
            stockChar.onClick.RemoveAllListeners();

            // Add conditional listener based on character state
            if (stockCharacter == null || stockCharacter.IsDead || configsHandler.IsCharacterInActive(stockCharacter))
            {
                stockChar.onClick.AddListener(() => CantSelect());
            }
            else
            {
                stockChar.onClick.AddListener(() => StockSelected(stockChar));
            }
        }

    }

    private void Update()
    {

        transform.position = origin; // Set the position


        foreach (var stockChar in stockChars) // Update the stock characters
        {
            BattleCharacter stockCharacter = configsHandler.GetStockCharacter(stockChar.transform.GetSiblingIndex()); // Get the stock character

            TextMeshProUGUI healthAmount = stockChar.transform.GetChild(3).GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>();

            Image healthBar = stockChar.transform.GetChild(3).GetChild(0).GetComponent<Image>();

            // Update onClick listener based on current character state
            stockChar.onClick.RemoveAllListeners();
            if (stockCharacter == null || stockCharacter.IsDead || configsHandler.IsCharacterInActive(stockCharacter))
            {
                stockChar.onClick.AddListener(() => CantSelect());
            }
            else
            {
                stockChar.onClick.AddListener(() => StockSelected(stockChar));
            }

            if (stockCharacter == null) // If the stock character is null set the stock character info to empty
            {
                stockChar.image.color = Color.gray;
                stockChar.transform.GetChild(0).gameObject.SetActive(false);
                stockChar.transform.GetChild(1).gameObject.SetActive(false);
                stockChar.transform.GetChild(2).gameObject.SetActive(false);
                stockChar.transform.GetChild(3).gameObject.SetActive(false);
                stockChar.transform.GetChild(4).gameObject.SetActive(false);
                stockChar.transform.GetChild(5).gameObject.SetActive(false);
                stockChar.transform.GetChild(6).gameObject.SetActive(false);
                stockChar.transform.GetChild(7).gameObject.SetActive(false);
                stockChar.transform.GetChild(8).gameObject.SetActive(false);
            }
            else if (stockCharacter.IsDead) // If the stock character is dead set the stock character info to dead
            {
                stockChar.image.color = new Color(1f, 0f, 0f, 0.6f);
                stockChar.transform.GetChild(0).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = stockCharacter.name;
                stockChar.transform.GetChild(2).gameObject.SetActive(true);
                stockChar.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "Lv: " + stockCharacter.level.ToString();
                stockChar.transform.GetChild(3).gameObject.SetActive(true);

                healthBar.fillAmount = stockCharacter.hP / (float)stockCharacter.maxHP;
                healthBar.color = Tools.InterpolateColor(Tools.FromArgb(178, 250, 67), Color.red, stockCharacter.hP / (float)stockCharacter.maxHP, 0.05f);

                healthAmount.text = Mathf.Max(0, stockCharacter.hP).ToString();


                stockChar.transform.GetChild(4).gameObject.SetActive(true);
                stockChar.transform.GetChild(5).gameObject.SetActive(true);
                stockChar.transform.GetChild(6).gameObject.SetActive(true);

                Types type1 = stockCharacter.skills.GetHighestSpDef();
                stockChar.transform.GetChild(4).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type1}");
                Types type2 = stockCharacter.skills.GetHighestSpAtk();
                stockChar.transform.GetChild(5).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type2}");
                Types type3 = stockCharacter.skills.GetLowestSpDef();
                stockChar.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type3}");

                stockChar.transform.GetChild(7).gameObject.SetActive(true);
                stockChar.transform.GetChild(7).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                stockChar.transform.GetChild(8).gameObject.SetActive(true);
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().text = "DEAD";
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().color = Color.white;
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().fontSize = 16;
            }
            else if (configsHandler.IsCharacterInActive(stockCharacter))
            {
                stockChar.image.color = Color.gray;
                stockChar.transform.GetChild(0).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = stockCharacter.name;
                stockChar.transform.GetChild(2).gameObject.SetActive(true);
                stockChar.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "Lv: " + stockCharacter.level.ToString();
                stockChar.transform.GetChild(3).gameObject.SetActive(true);

                healthBar.fillAmount = stockCharacter.hP / (float)stockCharacter.maxHP;
                healthBar.color = Tools.InterpolateColor(Tools.FromArgb(178, 250, 67) * 0.5f, Color.red * 0.5f, stockCharacter.hP / (float)stockCharacter.maxHP, 0.05f);

                healthAmount.text = stockCharacter.hP.ToString();

                healthAmount.color = Color.gray;

                stockChar.transform.GetChild(4).gameObject.SetActive(true);
                stockChar.transform.GetChild(5).gameObject.SetActive(true);
                stockChar.transform.GetChild(6).gameObject.SetActive(true);

                Types type1 = stockCharacter.skills.GetHighestSpDef();
                stockChar.transform.GetChild(4).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type1}");
                Types type2 = stockCharacter.skills.GetHighestSpAtk();
                stockChar.transform.GetChild(5).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type2}");
                Types type3 = stockCharacter.skills.GetLowestSpDef();
                stockChar.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type3}");

                stockChar.transform.GetChild(7).gameObject.SetActive(true);
                stockChar.transform.GetChild(7).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                stockChar.transform.GetChild(8).gameObject.SetActive(true);
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().color = Color.white;
                stockChar.transform.GetChild(8).GetComponent<TextMeshProUGUI>().fontSize = 16;

            }
            else // otherwise set the stock character info to the stock character
            {
                stockChar.image.color = Color.white;
                stockChar.transform.GetChild(0).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).gameObject.SetActive(true);
                stockChar.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = stockCharacter.name;
                stockChar.transform.GetChild(2).gameObject.SetActive(true);
                stockChar.transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "Lv: " + stockCharacter.level.ToString();
                stockChar.transform.GetChild(3).gameObject.SetActive(true);

                healthBar.fillAmount = stockCharacter.hP / (float)stockCharacter.maxHP;
                healthBar.color = Tools.InterpolateColor(Tools.FromArgb(178, 250, 67), Color.red, stockCharacter.hP / (float)stockCharacter.maxHP, 0.05f);

                healthAmount.text = stockCharacter.hP.ToString();

                healthAmount.color = Color.white;

                stockChar.transform.GetChild(4).gameObject.SetActive(true);
                stockChar.transform.GetChild(5).gameObject.SetActive(true);
                stockChar.transform.GetChild(6).gameObject.SetActive(true);

                Types type1 = stockCharacter.skills.GetHighestSpDef();
                stockChar.transform.GetChild(4).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type1}");
                Types type2 = stockCharacter.skills.GetHighestSpAtk();
                stockChar.transform.GetChild(5).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type2}");
                Types type3 = stockCharacter.skills.GetLowestSpDef();
                stockChar.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/BattleEffects/{type3}");

                stockChar.transform.GetChild(7).gameObject.SetActive(false);
                stockChar.transform.GetChild(8).gameObject.SetActive(false);
            }
        }
    }

    private void StockSelected(Button self) // Selects the stock character
    {
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        configsHandler.SetPlayerCharacter(self.transform.GetSiblingIndex()); // Set the player character

        gameObject.SetActive(false); // Disable the stock players UI
        gameObject.transform.parent.parent.gameObject.SetActive(false); // Disable the action menu
    }

    private void CantSelect()
    {
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/RejectedSelect"));

    }
}
