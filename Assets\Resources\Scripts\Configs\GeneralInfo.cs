using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Singleton manager for general game information including modifiers and character rarity tiers
/// Provides global access to modifier and tier data through key-based lookup
/// Loads data from generalInfo.json and handles graceful fallbacks for missing data
///
/// Usage:
/// - GeneralInfo.GetModifierSkill("MO0") returns "Inteligência"
/// - GeneralInfo.GetTierName("TR1") returns "Épico"
/// - GeneralInfo.GetTierColor("TR1") returns "#7030a0"
///
/// The system is read-only from game code perspective - all modifications must come through import process
/// </summary>
public class GeneralInfo : MonoBehaviour
{
    #region Singleton Implementation
    private static GeneralInfo _instance;

    /// <summary>
    /// Singleton instance accessor
    /// Automatically creates instance if none exists
    /// </summary>
    public static GeneralInfo Instance
    {
        get
        {
            if (_instance == null)
            {
                // Try to find existing instance in scene
                _instance = FindFirstObjectByType<GeneralInfo>();

                if (_instance == null)
                {
                    // Create new GameObject with GeneralInfo component
                    GameObject generalInfoObject = new GameObject("GeneralInfo");
                    _instance = generalInfoObject.AddComponent<GeneralInfo>();
                    DontDestroyOnLoad(generalInfoObject);
                }
            }
            return _instance;
        }
    }
    #endregion

    #region Private Fields
    /// <summary>
    /// Dictionary for fast modifier lookup by id
    /// Key: string id (e.g., "MO0")
    /// Value: JsonModifier object containing all modifier data
    /// </summary>
    private Dictionary<string, JsonModifier> modifierLookup = new Dictionary<string, JsonModifier>();

    /// <summary>
    /// Dictionary for fast tier lookup by id
    /// Key: string id (e.g., "TR1")
    /// Value: JsonTier object containing all tier data
    /// </summary>
    private Dictionary<string, JsonTier> tierLookup = new Dictionary<string, JsonTier>();

    /// <summary>
    /// Dictionary for fast moon phase lookup by id
    /// Key: string id (e.g., "MMRANG24")
    /// Value: JsonMoonRange object containing all moon phase data
    /// </summary>
    private readonly Dictionary<string, JsonMoonRange> moonPhaseLookup = new();

    /// <summary>
    /// Flag to track if data has been loaded successfully
    /// </summary>
    private bool isLoaded = false;

    /// <summary>
    /// Cached reference to loaded general info data for debugging and inspection
    /// </summary>
    private JsonGeneralInfo generalInfo;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        // Ensure singleton pattern
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            LoadGeneralInfo();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    #endregion

    #region Modifiers Region
    /// <summary>
    /// Gets the skill name for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Skill name or the id itself if not found</returns>
    public static string GetModifierSkill(string id)
    {
        return Instance.GetModifierSkillInternal(id);
    }

    /// <summary>
    /// Gets the description for the specified modifier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Description text or empty string if not found</returns>
    public static string GetModifierDescription(string id)
    {
        return Instance.GetModifierDescriptionInternal(id);
    }

    /// <summary>
    /// Gets the acronym for the specified modifier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>Acronym or the id itself if not found</returns>
    public static string GetModifierAcronym(string id)
    {
        return Instance.GetModifierAcronymInternal(id);
    }

    /// <summary>
    /// Gets the complete modifier object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The modifier id to look up (e.g., "MO0")</param>
    /// <returns>JsonModifier object or null if not found</returns>
    public static JsonModifier GetModifier(string id)
    {
        return Instance.GetModifierInternal(id);
    }

    /// <summary>
    /// Gets all available modifier ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available modifier ids</returns>
    public static string[] GetAllModifierIds()
    {
        return Instance.GetAllModifierIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded modifiers
    /// </summary>
    /// <returns>Number of modifiers currently loaded</returns>
    public static int GetModifierCount()
    {
        return Instance.modifierLookup.Count;
    }

    // Convenience methods for the 6 specific modifiers

    // Skill name methods
    /// <summary>Gets the skill name for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeSkillName() => GetModifierSkill("MO0");

    /// <summary>Gets the skill name for Luck modifier (MO1)</summary>
    public static string GetLuckSkillName() => GetModifierSkill("MO1");

    /// <summary>Gets the skill name for Speed modifier (MO2)</summary>
    public static string GetSpeedSkillName() => GetModifierSkill("MO2");

    /// <summary>Gets the skill name for Evasion modifier (MO3)</summary>
    public static string GetEvasionSkillName() => GetModifierSkill("MO3");

    /// <summary>Gets the skill name for Precision modifier (MO4)</summary>
    public static string GetPrecisionSkillName() => GetModifierSkill("MO4");

    /// <summary>Gets the skill name for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceSkillName() => GetModifierSkill("MO5");

    /// <summary>Gets the skill name for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceSkillName() => GetModifierSkill("MO6");


    // Acronym methods
    /// <summary>Gets the acronym for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeAcronym() => GetModifierAcronym("MO0");

    /// <summary>Gets the acronym for Luck modifier (MO1)</summary>
    public static string GetLuckAcronym() => GetModifierAcronym("MO1");

    /// <summary>Gets the acronym for Speed modifier (MO2)</summary>
    public static string GetSpeedAcronym() => GetModifierAcronym("MO2");

    /// <summary>Gets the acronym for Evasion modifier (MO3)</summary>
    public static string GetEvasionAcronym() => GetModifierAcronym("MO3");

    /// <summary>Gets the acronym for Precision modifier (MO4)</summary>
    public static string GetPrecisionAcronym() => GetModifierAcronym("MO4");

    /// <summary>Gets the acronym for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceAcronym() => GetModifierAcronym("MO5");

    /// <summary>Gets the acronym for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceAcronym() => GetModifierAcronym("MO6");


    // Description methods
    /// <summary>Gets the description for Knowledge modifier (MO0)</summary>
    public static string GetKnowledgeDescription() => GetModifierDescription("MO0");

    /// <summary>Gets the description for Luck modifier (MO1)</summary>
    public static string GetLuckDescription() => GetModifierDescription("MO1");

    /// <summary>Gets the description for Speed modifier (MO2)</summary>
    public static string GetSpeedDescription() => GetModifierDescription("MO2");

    /// <summary>Gets the description for Evasion modifier (MO3)</summary>
    public static string GetEvasionDescription() => GetModifierDescription("MO3");

    /// <summary>Gets the description for Precision modifier (MO4)</summary>
    public static string GetPrecisionDescription() => GetModifierDescription("MO4");

    /// <summary>Gets the description for Critical Chance modifier (MO5)</summary>
    public static string GetCriticalChanceDescription() => GetModifierDescription("MO5");

    /// <summary>Gets the description for Parry Chance modifier (MO6)</summary>
    public static string GetParryChanceDescription() => GetModifierDescription("MO6");

    #endregion

    #region Character Rarity Region
    /// <summary>
    /// Gets the display name for the specified tier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Tier name or the id itself if not found</returns>
    public static string GetTierName(string id)
    {
        return Instance.GetTierNameInternal(id);
    }

    /// <summary>
    /// Gets the variance for the specified tier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Variance text or empty string if not found</returns>
    public static string GetTierVariance(string id)
    {
        return Instance.GetTierVarianceInternal(id);
    }

    /// <summary>
    /// Gets the color for the specified tier id
    /// Returns Color.white if not found or if color parsing fails (white fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Unity Color object or Color.white if not found/invalid</returns>
    public static Color GetTierColor(string id)
    {
        return Instance.GetTierColorInternal(id);
    }

    /// <summary>
    /// Gets the acronym for the specified tier id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>Acronym or the id itself if not found</returns>
    public static string GetTierAcronym(string id)
    {
        return Instance.GetTierAcronymInternal(id);
    }

    /// <summary>
    /// Gets the selectDrop value for the specified tier id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>SelectDrop value or empty string if not found</returns>
    public static string GetTierSelectDrop(string id)
    {
        return Instance.GetTierSelectDropInternal(id);
    }

    /// <summary>
    /// Gets the complete tier object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The tier id to look up (e.g., "TR1")</param>
    /// <returns>JsonTier object or null if not found</returns>
    public static JsonTier GetTier(string id)
    {
        return Instance.GetTierInternal(id);
    }

    /// <summary>
    /// Gets all available tier ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available tier ids</returns>
    public static string[] GetAllTierIds()
    {
        return Instance.GetAllTierIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded tiers
    /// </summary>
    /// <returns>Number of tiers currently loaded</returns>
    public static int GetTierCount()
    {
        return Instance.tierLookup.Count;
    }
    #endregion

    #region Moon Phases Region
    /// <summary>
    /// Gets the complete moon phase object for advanced usage
    /// Returns null if id not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>JsonMoonRange object or null if not found</returns>
    public static JsonMoonRange GetMoonPhase(string id)
    {
        return Instance.GetMoonPhaseInternal(id);
    }

    /// <summary>
    /// Gets the display name for the specified moon phase id
    /// Returns the id itself if not found (graceful fallback)
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Moon phase display name or the id itself if not found</returns>
    public static string GetMoonPhaseName(string id)
    {
        return Instance.GetMoonPhaseNameInternal(id);
    }

    /// <summary>
    /// Gets the technical nomenclature for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Technical nomenclature or empty string if not found</returns>
    public static string GetMoonPhaseTechnicalName(string id)
    {
        return Instance.GetMoonPhaseTechnicalNameInternal(id);
    }

    /// <summary>
    /// Gets a specific knowledge value for a moon phase
    /// Returns empty string if moon phase or knowledge id not found
    /// </summary>
    /// <param name="moonPhaseId">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <param name="knowledgeId">The knowledge id to find (e.g., "KN1")</param>
    /// <returns>Knowledge value or empty string if not found</returns>
    public static string GetMoonPhaseKnowledgeValue(string moonPhaseId, string knowledgeId)
    {
        return Instance.GetMoonPhaseKnowledgeValueInternal(moonPhaseId, knowledgeId);
    }

    /// <summary>
    /// Gets a specific attribute value for a moon phase
    /// Returns empty string if moon phase or attribute id not found
    /// </summary>
    /// <param name="moonPhaseId">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <param name="attributeId">The attribute id to find (e.g., "AT1")</param>
    /// <returns>Attribute value or empty string if not found</returns>
    public static string GetMoonPhaseAttributeValue(string moonPhaseId, string attributeId)
    {
        return Instance.GetMoonPhaseAttributeValueInternal(moonPhaseId, attributeId);
    }

    /// <summary>
    /// Gets the party damage modifier for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Party damage modifier or empty string if not found</returns>
    public static string GetMoonPhaseDamageParty(string id)
    {
        return Instance.GetMoonPhaseDamagePartyInternal(id);
    }

    /// <summary>
    /// Gets the opponent damage modifier for the specified moon phase id
    /// Returns empty string if not found
    /// </summary>
    /// <param name="id">The moon phase id to look up (e.g., "MMRANG24")</param>
    /// <returns>Opponent damage modifier or empty string if not found</returns>
    public static string GetMoonPhaseDamageOpponent(string id)
    {
        return Instance.GetMoonPhaseDamageOpponentInternal(id);
    }

    /// <summary>
    /// Gets all available moon phase ids for debugging or UI population
    /// </summary>
    /// <returns>Array of all available moon phase ids</returns>
    public static string[] GetAllMoonPhaseIds()
    {
        return Instance.GetAllMoonPhaseIdsInternal();
    }

    /// <summary>
    /// Gets the total number of loaded moon phases
    /// </summary>
    /// <returns>Number of moon phases currently loaded</returns>
    public static int GetMoonPhaseCount()
    {
        return Instance.moonPhaseLookup.Count;
    }
    #endregion

    #region Public Utility Methods
    /// <summary>
    /// Forces reload of general info data from generalInfo.json
    /// Useful after import operations or for debugging
    /// </summary>
    public static void ReloadGeneralInfo()
    {
        Instance.LoadGeneralInfo();
    }

    /// <summary>
    /// Gets loading status for debugging
    /// </summary>
    /// <returns>True if data is loaded successfully</returns>
    public bool IsLoaded()
    {
        return isLoaded;
    }
    #endregion

    #region Private Implementation Methods
    /// <summary>
    /// Internal method to load modifiers and tiers from generalInfo.json
    /// Called automatically on initialization and can be called manually to reload
    /// </summary>
    private void LoadGeneralInfo()
    {
        try
        {
            // Clear existing data
            modifierLookup.Clear();
            tierLookup.Clear();
            moonPhaseLookup.Clear();
            isLoaded = false;

            // Load from JSON using existing JsonSaveHelper pattern
            if (JsonSaveHelper.FileExists("generalInfo.json"))
            {
                generalInfo = JsonSaveHelper.LoadFromJson<JsonGeneralInfo>("generalInfo.json");

                if (generalInfo != null)
                {
                    int modifierCount = 0;
                    int tierCount = 0;
                    int moonPhaseCount = 0;

                    // Build modifier lookup dictionary
                    if (generalInfo.modifierListPkg != null)
                    {
                        foreach (var modifier in generalInfo.modifierListPkg)
                        {
                            if (modifier != null && !string.IsNullOrEmpty(modifier.id))
                            {
                                // Use id as dictionary key for fast lookup
                                modifierLookup[modifier.id] = modifier;
                                modifierCount++;
                            }
                        }
                    }

                    // Build tier lookup dictionary
                    if (generalInfo.tierListPkg != null)
                    {
                        foreach (var tier in generalInfo.tierListPkg)
                        {
                            if (tier != null && !string.IsNullOrEmpty(tier.id))
                            {
                                // Use id as dictionary key for fast lookup
                                tierLookup[tier.id] = tier;
                                tierCount++;
                            }
                        }
                    }

                    // Build moon phase lookup dictionary
                    if (generalInfo.modMoonRangesPkg != null)
                    {
                        foreach (var moonPhase in generalInfo.modMoonRangesPkg)
                        {
                            if (moonPhase != null && !string.IsNullOrEmpty(moonPhase.id))
                            {
                                // Use id as dictionary key for fast lookup
                                moonPhaseLookup[moonPhase.id] = moonPhase;
                                moonPhaseCount++;
                            }
                        }
                    }

                    isLoaded = true;
                    //Debug.Log($"[GeneralInfo] ✅ Loaded {modifierCount} modifiers, {tierCount} tiers, and {moonPhaseCount} moon phases from generalInfo.json");
                }
                else
                {
                    Debug.LogWarning("[GeneralInfo] ⚠️ generalInfo.json exists but contains no data");
                }
            }
            else
            {
                Debug.LogWarning("[GeneralInfo] ⚠️ generalInfo.json not found - modifiers and tiers not available");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[GeneralInfo] ❌ Failed to load general info data: {ex.Message}");
            isLoaded = false;
        }
    }

    // Modifier internal implementation methods
    private string GetModifierSkillInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.skill ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetModifierDescriptionInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.description ?? "";
        }

        return "";
    }

    private string GetModifierAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (modifierLookup.TryGetValue(id, out JsonModifier modifier))
        {
            return modifier.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Modifier id '{id}' not found, returning id as fallback");
        return id;
    }

    private JsonModifier GetModifierInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        modifierLookup.TryGetValue(id, out JsonModifier modifier);
        return modifier;
    }

    private string[] GetAllModifierIdsInternal()
    {
        return modifierLookup.Keys.ToArray();
    }

    // Tier internal implementation methods
    private string GetTierNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.name ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetTierVarianceInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.variance ?? "";
        }

        return "";
    }

    private Color GetTierColorInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return Color.white;

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            string colorHex = tier.color;

            // Try to parse the hex color string
            if (!string.IsNullOrEmpty(colorHex))
            {
                if (ColorUtility.TryParseHtmlString(colorHex, out Color parsedColor))
                {
                    return parsedColor;
                }
                else
                {
                    Debug.LogWarning($"[GeneralInfo] ⚠️ Invalid color format '{colorHex}' for tier '{id}', returning white as fallback");
                    return Color.white;
                }
            }
            else
            {
                Debug.LogWarning($"[GeneralInfo] ⚠️ Empty color value for tier '{id}', returning white as fallback");
                return Color.white;
            }
        }

        // Graceful fallback - return white color
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning white color as fallback");
        return Color.white;
    }

    private string GetTierAcronymInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.acronym ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Tier id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetTierSelectDropInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (tierLookup.TryGetValue(id, out JsonTier tier))
        {
            return tier.selectDrop ?? "";
        }

        return "";
    }

    private JsonTier GetTierInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        tierLookup.TryGetValue(id, out JsonTier tier);
        return tier;
    }

    private string[] GetAllTierIdsInternal()
    {
        return tierLookup.Keys.ToArray();
    }

    // Moon phase internal implementation methods
    private JsonMoonRange GetMoonPhaseInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return null;

        moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase);
        return moonPhase;
    }

    private string GetMoonPhaseNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.moonPhase ?? id;
        }

        // Graceful fallback - return the id itself
        Debug.LogWarning($"[GeneralInfo] ⚠️ Moon phase id '{id}' not found, returning id as fallback");
        return id;
    }

    private string GetMoonPhaseTechnicalNameInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.technicalNomenclature ?? "";
        }

        return "";
    }

    private string GetMoonPhaseKnowledgeValueInternal(string moonPhaseId, string knowledgeId)
    {
        if (string.IsNullOrEmpty(moonPhaseId) || string.IsNullOrEmpty(knowledgeId))
            return "";

        if (moonPhaseLookup.TryGetValue(moonPhaseId, out JsonMoonRange moonPhase))
        {
            if (moonPhase.knowledge != null)
            {
                foreach (var knowledge in moonPhase.knowledge)
                {
                    if (knowledge != null && knowledge.id == knowledgeId)
                    {
                        return knowledge.value ?? "";
                    }
                }
            }
        }

        return "";
    }

    private string GetMoonPhaseAttributeValueInternal(string moonPhaseId, string attributeId)
    {
        if (string.IsNullOrEmpty(moonPhaseId) || string.IsNullOrEmpty(attributeId))
            return "";

        if (moonPhaseLookup.TryGetValue(moonPhaseId, out JsonMoonRange moonPhase))
        {
            if (moonPhase.attribute != null)
            {
                foreach (var attribute in moonPhase.attribute)
                {
                    if (attribute != null && attribute.id == attributeId)
                    {
                        return attribute.value ?? "";
                    }
                }
            }
        }

        return "";
    }

    private string GetMoonPhaseDamagePartyInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.modDanoParty ?? "";
        }

        return "";
    }

    private string GetMoonPhaseDamageOpponentInternal(string id)
    {
        if (string.IsNullOrEmpty(id))
            return "";

        if (moonPhaseLookup.TryGetValue(id, out JsonMoonRange moonPhase))
        {
            return moonPhase.modDanoOponente ?? "";
        }

        return "";
    }

    private string[] GetAllMoonPhaseIdsInternal()
    {
        return moonPhaseLookup.Keys.ToArray();
    }
    #endregion

    #region Debug and Utility Methods
    /// <summary>
    /// Debug method to print all loaded modifiers to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Modifiers")]
    public void DebugPrintAllModifiers()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Modifiers ({modifierLookup.Count} total):");
        foreach (var kvp in modifierLookup)
        {
            var modifier = kvp.Value;
            Debug.Log($"  ID: '{modifier.id}' | Skill: '{modifier.skill}' | Acronym: '{modifier.acronym}' | Description: '{modifier.description}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded tiers to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Tiers")]
    public void DebugPrintAllTiers()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Tiers ({tierLookup.Count} total):");
        foreach (var kvp in tierLookup)
        {
            var tier = kvp.Value;
            Color parsedColor = GetTierColorInternal(tier.id);
            Debug.Log($"  ID: '{tier.id}' | Name: '{tier.name}' | Acronym: '{tier.acronym}' | Color: '{tier.color}' (Parsed: {parsedColor}) | Variance: '{tier.variance}' | SelectDrop: '{tier.selectDrop}'");
        }
    }

    /// <summary>
    /// Debug method to print all loaded moon phases to console
    /// Useful for development and troubleshooting
    /// </summary>
    [ContextMenu("Debug Print All Moon Phases")]
    public void DebugPrintAllMoonPhases()
    {
        if (!isLoaded)
        {
            Debug.Log("[GeneralInfo] 🔍 No data loaded");
            return;
        }

        Debug.Log($"[GeneralInfo] 🔍 Loaded Moon Phases ({moonPhaseLookup.Count} total):");
        foreach (var kvp in moonPhaseLookup)
        {
            var moonPhase = kvp.Value;
            Debug.Log($"  ID: '{moonPhase.id}' | Phase: '{moonPhase.moonPhase}' | Technical: '{moonPhase.technicalNomenclature}'");
            Debug.Log($"    Party Damage: '{moonPhase.modDanoParty}' | Opponent Damage: '{moonPhase.modDanoOponente}'");

            if (moonPhase.knowledge != null && moonPhase.knowledge.Length > 0)
            {
                var knowledgeValues = moonPhase.knowledge.Select(k => $"{k.id}:{k.value}").ToArray();
                Debug.Log($"    Knowledge: [{string.Join(", ", knowledgeValues)}]");
            }

            if (moonPhase.attribute != null && moonPhase.attribute.Length > 0)
            {
                var attributeValues = moonPhase.attribute.Select(a => $"{a.id}:{a.value}").ToArray();
                Debug.Log($"    Attributes: [{string.Join(", ", attributeValues)}]");
            }
        }
    }

    /// <summary>
    /// Debug method to print all loaded data (modifiers, tiers, and moon phases)
    /// </summary>
    [ContextMenu("Debug Print All General Info")]
    public void DebugPrintAllGeneralInfo()
    {
        DebugPrintAllModifiers();
        DebugPrintAllTiers();
        DebugPrintAllMoonPhases();
    }
    #endregion
}
